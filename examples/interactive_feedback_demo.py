#!/usr/bin/env python3
"""
Interactive Feedback System Demo

This script demonstrates the new interactive feedback system that allows
users to provide feedback at each stage of the 3D model generation process.

Inspired by mcp-feedback-enhanced, this demo shows:
- Step-by-step user feedback collection
- Dynamic workflow adjustment
- Intelligent auto-proceed mechanisms
- Multi-interface support

Usage:
    # CLI interface (default)
    python examples/interactive_feedback_demo.py

    # Web interface
    python examples/interactive_feedback_demo.py --interface web

    # Desktop interface
    python examples/interactive_feedback_demo.py --interface desktop

Requirements:
    - Set up environment: conda activate bl4.4env
    - Ensure all dependencies are installed
"""

import asyncio
import argparse
import sys
import os
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from interactive_feedback import (
    InteractiveOrchestratorAgent,
    InteractiveOrchestrationConfig,
    FeedbackStage,
    FeedbackOption,
    FeedbackAction,
    AutoProceedConfig
)
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def demo_basic_feedback_flow():
    """Demonstrate basic feedback flow with CLI interface."""
    print("\n🎯 Demo 1: Basic Interactive Feedback Flow")
    print("=" * 60)
    
    try:
        # Create configuration
        config = InteractiveOrchestrationConfig(
            feedback_interface_type="cli",
            enable_auto_proceed=True,
            auto_proceed_confidence_threshold=0.8,
            feedback_timeout=60.0
        )
        
        # Initialize orchestrator
        orchestrator = InteractiveOrchestratorAgent(config)
        
        # Simulate a simple workflow
        print("🚀 启动交互式3D模型生成流程...")
        
        # Mock image path for demo
        demo_image_path = "demo_images/sample_cube.png"
        
        # Execute interactive orchestration
        result = await orchestrator.orchestrate_task_interactive(
            image_path=demo_image_path,
            user_preferences={"style": "realistic", "quality": "high"},
            model_name="Interactive_Demo_Model",
            description="Demo model with interactive feedback"
        )
        
        if result.success:
            print(f"\n✅ 交互式生成完成!")
            print(f"📁 模型文件: {result.model_path}")
            print(f"🖼️ 渲染图: {result.render_path}")
            print(f"⏱️ 总耗时: {result.execution_time:.2f}秒")
            
            # Display feedback summary
            feedback_summary = result.feedback_history
            print(f"\n📊 反馈统计:")
            print(f"   - 总请求数: {feedback_summary.get('total_requests', 0)}")
            print(f"   - 总响应数: {feedback_summary.get('total_responses', 0)}")
            print(f"   - 涉及阶段: {', '.join(feedback_summary.get('stages_covered', []))}")
        else:
            print(f"\n❌ 生成失败: {result.error}")
    
    except Exception as e:
        print(f"\n💥 Demo失败: {e}")
        logger.exception("Demo error")


async def demo_adaptive_learning():
    """Demonstrate adaptive learning capabilities."""
    print("\n🧠 Demo 2: Adaptive Learning and User Preferences")
    print("=" * 60)
    
    from interactive_feedback.analytics import UserPreferenceManager, AdaptiveStrategy
    
    # Create preference manager
    preference_manager = UserPreferenceManager(user_id="demo_user")
    
    # Simulate some user patterns
    print("📚 模拟用户行为模式...")
    
    from interactive_feedback.core import FeedbackResponse
    from datetime import datetime
    
    # Simulate user always proceeding on image analysis
    for i in range(5):
        response = FeedbackResponse(
            request_id=f"req_{i}",
            session_id="demo_session",
            stage=FeedbackStage.IMAGE_ANALYSIS,
            action=FeedbackAction.PROCEED,
            response_time_seconds=10.0 + i * 2,
            confidence=0.9
        )
        preference_manager.update_pattern(response)
    
    # Simulate user often retrying code generation
    for i in range(3):
        response = FeedbackResponse(
            request_id=f"req_code_{i}",
            session_id="demo_session",
            stage=FeedbackStage.CODE_GENERATION,
            action=FeedbackAction.RETRY,
            response_time_seconds=25.0 + i * 5,
            confidence=0.6
        )
        preference_manager.update_pattern(response)
    
    # Analyze patterns
    print("\n📈 用户偏好分析:")
    
    # Image analysis preferences
    preferred_action = preference_manager.get_preferred_action(FeedbackStage.IMAGE_ANALYSIS)
    avg_time = preference_manager.get_avg_response_time(FeedbackStage.IMAGE_ANALYSIS)
    confidence_threshold = preference_manager.get_confidence_threshold(FeedbackStage.IMAGE_ANALYSIS)
    
    print(f"   图像分析阶段:")
    print(f"     - 偏好动作: {preferred_action.value if preferred_action else 'None'}")
    print(f"     - 平均响应时间: {avg_time:.1f}秒")
    print(f"     - 置信度阈值: {confidence_threshold:.2f}")
    
    # Code generation preferences
    preferred_action = preference_manager.get_preferred_action(FeedbackStage.CODE_GENERATION)
    avg_time = preference_manager.get_avg_response_time(FeedbackStage.CODE_GENERATION)
    confidence_threshold = preference_manager.get_confidence_threshold(FeedbackStage.CODE_GENERATION)
    
    print(f"   代码生成阶段:")
    print(f"     - 偏好动作: {preferred_action.value if preferred_action else 'None'}")
    print(f"     - 平均响应时间: {avg_time:.1f}秒")
    print(f"     - 置信度阈值: {confidence_threshold:.2f}")
    
    # Test adaptive strategy
    print("\n🎯 自适应策略测试:")
    adaptive_strategy = AdaptiveStrategy()
    
    # Test auto-proceed decision
    should_proceed = adaptive_strategy.should_auto_proceed(
        stage=FeedbackStage.IMAGE_ANALYSIS,
        confidence=0.95,
        user_preferences={},
        success_rate=0.9
    )
    print(f"   图像分析自动继续建议: {'是' if should_proceed else '否'}")
    
    optimal_timeout = adaptive_strategy.calculate_optimal_timeout(
        stage=FeedbackStage.IMAGE_ANALYSIS,
        user_preferences={}
    )
    print(f"   建议超时时间: {optimal_timeout}秒")


async def demo_multi_interface():
    """Demonstrate different interface types."""
    print("\n🖥️ Demo 3: Multi-Interface Support")
    print("=" * 60)
    
    from interactive_feedback.interfaces import FeedbackInterfaceFactory
    from interactive_feedback.core import InteractiveFeedbackManager, FeedbackRequest
    
    interfaces_to_test = ["cli"]  # Only CLI for this demo
    
    for interface_type in interfaces_to_test:
        print(f"\n🔧 测试 {interface_type.upper()} 界面...")
        
        try:
            # Create feedback manager
            feedback_manager = InteractiveFeedbackManager(
                interface_type=interface_type,
                config={"feedback_timeout": 30}
            )
            
            # Create a sample feedback request
            options = [
                FeedbackOption(
                    id="proceed",
                    label="继续",
                    action=FeedbackAction.PROCEED,
                    description="对结果满意，继续下一步",
                    shortcut="1"
                ),
                FeedbackOption(
                    id="retry",
                    label="重试",
                    action=FeedbackAction.RETRY,
                    description="重新执行当前步骤",
                    shortcut="2"
                )
            ]
            
            auto_proceed = AutoProceedConfig(
                enabled=True,
                timeout_seconds=15,
                confidence_threshold=0.8
            )
            
            # Request feedback (with short timeout for demo)
            print(f"   📝 请求用户反馈...")
            
            response = await feedback_manager.request_feedback(
                stage=FeedbackStage.IMAGE_ANALYSIS,
                title="图像分析完成",
                content={
                    "detected_objects": [
                        {"type": "cube", "confidence": 0.95},
                        {"type": "sphere", "confidence": 0.87}
                    ],
                    "analysis_confidence": 0.91
                },
                options=options,
                auto_proceed=auto_proceed,
                timeout=30.0
            )
            
            print(f"   ✅ 收到反馈: {response.action.value}")
            
            await feedback_manager.close_session()
            
        except Exception as e:
            print(f"   ❌ {interface_type.upper()} 界面测试失败: {e}")


async def demo_analytics():
    """Demonstrate analytics and insights."""
    print("\n📊 Demo 4: Analytics and Insights")
    print("=" * 60)
    
    from interactive_feedback.analytics import FeedbackAnalytics
    
    # Create analytics system
    analytics = FeedbackAnalytics()
    
    # Simulate some session data
    print("📈 模拟分析数据...")
    
    sample_sessions = [
        {
            "session_id": "session_1",
            "total_requests": 5,
            "total_responses": 5,
            "stages_covered": ["image_analysis", "specification_generation", "code_generation"],
            "success_rate": 0.8,
            "user_satisfaction": 0.9,
            "total_time": 180
        },
        {
            "session_id": "session_2", 
            "total_requests": 6,
            "total_responses": 6,
            "stages_covered": ["image_analysis", "specification_generation", "code_generation", "model_generation"],
            "success_rate": 0.9,
            "user_satisfaction": 0.85,
            "total_time": 220
        },
        {
            "session_id": "session_3",
            "total_requests": 4,
            "total_responses": 3,
            "stages_covered": ["image_analysis", "specification_generation"],
            "success_rate": 0.6,
            "user_satisfaction": 0.7,
            "total_time": 150
        }
    ]
    
    for session in sample_sessions:
        analytics.record_session(session)
    
    # Get analytics
    overall_analytics = analytics.get_overall_analytics()
    
    print(f"\n📊 整体分析结果:")
    print(f"   - 总会话数: {overall_analytics['total_sessions']}")
    print(f"   - 平均用户满意度: {overall_analytics['avg_user_satisfaction']:.2f}")
    print(f"   - 平均完成时间: {overall_analytics['avg_completion_time']:.1f}秒")
    
    # Get insights
    insights = analytics.generate_insights()
    if insights:
        print(f"\n💡 改进建议:")
        for insight in insights:
            print(f"   - {insight}")
    else:
        print(f"\n✅ 系统运行良好，暂无改进建议")


async def main():
    """Run all demos."""
    parser = argparse.ArgumentParser(description="Interactive Feedback System Demo")
    parser.add_argument("--interface", choices=["cli", "web", "desktop"], default="cli",
                       help="Feedback interface type")
    parser.add_argument("--demo", choices=["all", "basic", "adaptive", "interface", "analytics"], 
                       default="all", help="Which demo to run")
    
    args = parser.parse_args()
    
    print("🚀 Interactive Feedback System Demo")
    print("=" * 60)
    print("这个演示展示了新的交互式反馈系统功能")
    print("灵感来源于 mcp-feedback-enhanced 项目")
    print()
    
    try:
        if args.demo in ["all", "basic"]:
            await demo_basic_feedback_flow()
        
        if args.demo in ["all", "adaptive"]:
            await demo_adaptive_learning()
        
        if args.demo in ["all", "interface"]:
            await demo_multi_interface()
        
        if args.demo in ["all", "analytics"]:
            await demo_analytics()
        
        print("\n🎉 所有演示完成!")
        print("\n💡 下一步:")
        print("   1. 集成到现有的Blender AI Agent系统")
        print("   2. 配置Web界面和桌面应用")
        print("   3. 在实际项目中测试用户体验")
        print("   4. 根据用户反馈持续优化")
        
    except KeyboardInterrupt:
        print("\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        logger.exception("Demo error")


if __name__ == "__main__":
    asyncio.run(main())
