#!/usr/bin/env python3
"""
Multi-LLM Provider Demo

This script demonstrates the new multi-LLM provider support in the Blender AI Agent system,
showing how to use different providers (OpenAI, Gemini, DeepSeek, Kimi) with automatic
fallback and cost optimization.

Usage:
    python examples/multi_llm_demo.py

Requirements:
    - Set up API keys in .env file
    - Install optional dependencies: pip install google-generativeai
"""

import os
import sys
import time
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from llm_providers import (
    LLMManager, LLMManagerConfig, LLMFactory, create_llm_provider,
    LLMMessage, MessageRole, FallbackStrategy, LoadBalancingStrategy
)
from llm_providers.base import LLMProviderType
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def demo_basic_provider_usage():
    """Demonstrate basic usage of different LLM providers."""
    print("\n🔧 Demo 1: Basic Provider Usage")
    print("=" * 50)
    
    # Test message
    messages = [
        LLMMessage(role=MessageRole.USER, content="Hello! Please respond with 'Hello from [Provider Name]'")
    ]
    
    providers_to_test = [
        ("openai", "gpt-4o-mini"),
        ("gemini", "gemini-1.5-flash"),
        ("deepseek", "deepseek-chat"),
        ("kimi", "moonshot-v1-8k")
    ]
    
    for provider_name, model in providers_to_test:
        try:
            print(f"\n📡 Testing {provider_name.upper()} ({model})...")
            
            # Create provider
            provider = create_llm_provider(
                provider=provider_name,
                model=model,
                temperature=0.1
            )
            
            # Test chat completion
            start_time = time.time()
            response = provider.chat_completion(messages)
            latency = time.time() - start_time
            
            print(f"   ✅ Response: {response.content}")
            print(f"   ⏱️  Latency: {latency:.2f}s")
            if response.usage:
                print(f"   💰 Cost: ${response.usage.cost_usd:.6f}")
                print(f"   🔢 Tokens: {response.usage.total_tokens}")
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")


def demo_llm_manager():
    """Demonstrate LLM Manager with multiple providers and fallback."""
    print("\n🎛️  Demo 2: LLM Manager with Fallback")
    print("=" * 50)
    
    # Configure manager
    config = LLMManagerConfig(
        fallback_strategy=FallbackStrategy.CHEAPEST_FIRST,
        load_balancing_strategy=LoadBalancingStrategy.LEAST_COST,
        enable_health_checks=True,
        cost_threshold=0.05,  # Max $0.05 per request
        latency_threshold=30.0  # Max 30 seconds
    )
    
    manager = LLMManager(config)
    
    # Add providers with different weights
    providers_config = [
        ("openai-mini", "openai", "gpt-4o-mini", 1.0),
        ("gemini-flash", "gemini", "gemini-1.5-flash", 2.0),  # Higher weight
        ("deepseek", "deepseek", "deepseek-chat", 1.5),
        ("kimi", "kimi", "moonshot-v1-8k", 1.0)
    ]
    
    for name, provider_type, model, weight in providers_config:
        try:
            provider = create_llm_provider(
                provider=provider_type,
                model=model,
                temperature=0.1
            )
            manager.add_provider(name, provider, weight)
            print(f"   ✅ Added {name} ({provider_type})")
        except Exception as e:
            print(f"   ⚠️  Skipped {name}: {e}")
    
    # Test multiple requests
    test_messages = [
        [LLMMessage(role=MessageRole.USER, content="What is 2+2?")],
        [LLMMessage(role=MessageRole.USER, content="Explain Python in one sentence.")],
        [LLMMessage(role=MessageRole.USER, content="What is the capital of France?")],
    ]
    
    print(f"\n📊 Testing {len(test_messages)} requests with automatic provider selection...")
    
    for i, messages in enumerate(test_messages, 1):
        try:
            print(f"\n   Request {i}: {messages[0].content}")
            
            start_time = time.time()
            response = manager.chat_completion(messages)
            latency = time.time() - start_time
            
            print(f"   ✅ Response: {response.content[:100]}...")
            print(f"   🏷️  Provider: {response.provider.value if response.provider else 'Unknown'}")
            print(f"   ⏱️  Latency: {latency:.2f}s")
            if response.usage and response.usage.cost_usd:
                print(f"   💰 Cost: ${response.usage.cost_usd:.6f}")
            
        except Exception as e:
            print(f"   ❌ Request {i} failed: {e}")
    
    # Show metrics summary
    print(f"\n📈 Provider Metrics Summary:")
    metrics = manager.get_metrics_summary()
    for provider_name, stats in metrics.items():
        print(f"   {provider_name}:")
        print(f"     - Requests: {stats['total_requests']}")
        print(f"     - Success Rate: {stats['success_rate']:.1%}")
        print(f"     - Avg Latency: {stats['average_latency']:.2f}s")
        print(f"     - Avg Cost: ${stats['average_cost']:.6f}")
        print(f"     - Health: {'✅' if stats['health_status'] else '❌'}")


def demo_cost_optimization():
    """Demonstrate cost optimization features."""
    print("\n💰 Demo 3: Cost Optimization")
    print("=" * 50)
    
    # Create providers with different cost profiles
    providers = {}
    
    try:
        # Expensive but high-quality
        providers['premium'] = create_llm_provider("openai", "gpt-4", temperature=0.1)
        print("   ✅ Added premium provider (GPT-4)")
    except:
        print("   ⚠️  Premium provider not available")
    
    try:
        # Balanced cost/performance
        providers['balanced'] = create_llm_provider("openai", "gpt-4o-mini", temperature=0.1)
        print("   ✅ Added balanced provider (GPT-4o-mini)")
    except:
        print("   ⚠️  Balanced provider not available")
    
    try:
        # Low cost
        providers['budget'] = create_llm_provider("deepseek", "deepseek-chat", temperature=0.1)
        print("   ✅ Added budget provider (DeepSeek)")
    except:
        print("   ⚠️  Budget provider not available")
    
    if not providers:
        print("   ❌ No providers available for cost optimization demo")
        return
    
    # Test same prompt with different providers
    messages = [
        LLMMessage(role=MessageRole.USER, content="Explain machine learning in 50 words.")
    ]
    
    print(f"\n💸 Cost comparison for the same prompt:")
    
    for name, provider in providers.items():
        try:
            response = provider.chat_completion(messages)
            cost = response.usage.cost_usd if response.usage else 0.0
            tokens = response.usage.total_tokens if response.usage else 0
            
            print(f"   {name.upper()}:")
            print(f"     - Cost: ${cost:.6f}")
            print(f"     - Tokens: {tokens}")
            print(f"     - Response: {response.content[:80]}...")
            
        except Exception as e:
            print(f"   {name.upper()}: ❌ {e}")


def demo_multimodal_support():
    """Demonstrate multimodal capabilities."""
    print("\n🖼️  Demo 4: Multimodal Support")
    print("=" * 50)
    
    # Check which providers support multimodal
    providers_to_test = [
        ("openai", "gpt-4o"),
        ("gemini", "gemini-1.5-pro"),
    ]
    
    for provider_name, model in providers_to_test:
        try:
            provider = create_llm_provider(provider=provider_name, model=model)
            
            if provider.supports_multimodal():
                print(f"   ✅ {provider_name.upper()} ({model}) supports multimodal")
            else:
                print(f"   ❌ {provider_name.upper()} ({model}) does not support multimodal")
                
        except Exception as e:
            print(f"   ⚠️  {provider_name.upper()}: {e}")


def demo_embedding_generation():
    """Demonstrate embedding generation."""
    print("\n🔢 Demo 5: Embedding Generation")
    print("=" * 50)
    
    text = "This is a test sentence for embedding generation."
    
    try:
        # OpenAI embeddings
        provider = create_llm_provider("openai", "gpt-4o-mini")
        embedding = provider.generate_embedding(text)
        
        print(f"   ✅ OpenAI embedding generated")
        print(f"   📏 Dimension: {len(embedding)}")
        print(f"   🔢 First 5 values: {embedding[:5]}")
        
    except Exception as e:
        print(f"   ❌ Embedding generation failed: {e}")


def main():
    """Run all demos."""
    print("🚀 Multi-LLM Provider Demo")
    print("=" * 50)
    print("This demo showcases the new multi-LLM provider support.")
    print("Make sure to set up your API keys in the .env file!")
    
    try:
        demo_basic_provider_usage()
        demo_llm_manager()
        demo_cost_optimization()
        demo_multimodal_support()
        demo_embedding_generation()
        
        print("\n🎉 Demo completed successfully!")
        print("\n💡 Next steps:")
        print("   1. Set up your preferred API keys in .env")
        print("   2. Update agent configurations to use LLMManager")
        print("   3. Monitor costs and performance in production")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        logger.exception("Demo error")


if __name__ == "__main__":
    main()
