"""
Interactive Orchestrator Agent that integrates feedback collection
into the main workflow orchestration.
"""

import asyncio
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, field
from datetime import datetime
import logging

from main_orchestrator import (
    OrchestratorAgent, 
    OrchestrationConfig,
    TaskContext,
    WorkflowState,
    OrchestrationResult
)
from .core import (
    InteractiveFeedbackManager,
    FeedbackStage,
    FeedbackOption,
    FeedbackAction,
    AutoProceedConfig,
    FeedbackResponse
)
from .analytics import AdaptiveStrategy

logger = logging.getLogger(__name__)


@dataclass
class InteractiveOrchestrationConfig(OrchestrationConfig):
    """Extended configuration for interactive orchestration."""
    
    # Feedback interface settings
    feedback_interface_type: str = "web"
    feedback_timeout: float = 300.0  # 5 minutes default
    enable_auto_proceed: bool = True
    auto_proceed_confidence_threshold: float = 0.8
    
    # Stage-specific settings
    stage_feedback_enabled: Dict[str, bool] = field(default_factory=lambda: {
        "image_preprocessing": True,
        "image_analysis": True,
        "specification_generation": True,
        "code_generation": True,
        "model_generation": True,
        "visual_critique": True
    })
    
    # Learning and adaptation
    enable_adaptive_learning: bool = True
    user_preference_weight: float = 0.3
    success_rate_weight: float = 0.4
    confidence_weight: float = 0.3


class StageExecutor:
    """Base class for stage executors with feedback integration."""
    
    def __init__(self, stage: FeedbackStage, orchestrator: 'InteractiveOrchestratorAgent'):
        self.stage = stage
        self.orchestrator = orchestrator
        self.adaptive_strategy = orchestrator.adaptive_strategy
    
    async def execute_with_feedback(self,
                                  task_context: TaskContext,
                                  workflow_state: WorkflowState,
                                  input_data: Any = None) -> Any:
        """Execute stage with feedback integration."""
        
        max_iterations = 3
        iteration = 0
        
        while iteration < max_iterations:
            try:
                # Execute the stage
                result = await self.execute_stage(task_context, workflow_state, input_data)
                
                # Check if feedback is enabled for this stage
                if not self._is_feedback_enabled():
                    return result
                
                # Prepare feedback request
                feedback_content = await self.prepare_feedback_content(result, task_context)
                feedback_options = self.get_feedback_options(iteration)
                auto_proceed_config = await self.get_auto_proceed_config(result, task_context)
                
                # Request user feedback
                feedback_response = await self.orchestrator.feedback_manager.request_feedback(
                    stage=self.stage,
                    title=self.get_stage_title(),
                    content=feedback_content,
                    options=feedback_options,
                    auto_proceed=auto_proceed_config,
                    timeout=self.orchestrator.config.feedback_timeout
                )
                
                # Process feedback response
                processed_result = await self.process_feedback_response(
                    feedback_response, result, task_context, input_data
                )
                
                if feedback_response.action == FeedbackAction.PROCEED:
                    return processed_result
                elif feedback_response.action == FeedbackAction.RETRY:
                    # Adjust parameters and retry
                    input_data = self.adjust_parameters(input_data, feedback_response.parameters)
                    iteration += 1
                    continue
                elif feedback_response.action == FeedbackAction.MANUAL_ADJUST:
                    # Apply manual adjustments and return
                    return self.apply_manual_adjustments(processed_result, feedback_response.parameters)
                elif feedback_response.action == FeedbackAction.SKIP:
                    # Skip this stage
                    return input_data
                elif feedback_response.action == FeedbackAction.ABORT:
                    # Abort the entire workflow
                    raise Exception("Workflow aborted by user")
                else:
                    # Unknown action, proceed with result
                    return processed_result
                    
            except Exception as e:
                logger.error(f"Stage {self.stage.value} execution failed: {e}")
                iteration += 1
                if iteration >= max_iterations:
                    raise
        
        raise Exception(f"Stage {self.stage.value} failed after {max_iterations} attempts")
    
    async def execute_stage(self, task_context: TaskContext, workflow_state: WorkflowState, input_data: Any) -> Any:
        """Execute the actual stage logic. Must be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement execute_stage")
    
    async def prepare_feedback_content(self, result: Any, task_context: TaskContext) -> Dict[str, Any]:
        """Prepare content for feedback display. Must be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement prepare_feedback_content")
    
    def get_feedback_options(self, iteration: int) -> List[FeedbackOption]:
        """Get available feedback options for this stage."""
        options = [
            FeedbackOption(
                id="proceed",
                label="继续下一步",
                action=FeedbackAction.PROCEED,
                description="对当前结果满意，继续下一步",
                icon="✅",
                shortcut="Enter"
            ),
            FeedbackOption(
                id="retry",
                label="重新执行",
                action=FeedbackAction.RETRY,
                description="重新执行当前步骤",
                icon="🔄",
                shortcut="R"
            )
        ]
        
        # Add manual adjust option if supported
        if self.supports_manual_adjustment():
            options.append(FeedbackOption(
                id="manual_adjust",
                label="手动调整",
                action=FeedbackAction.MANUAL_ADJUST,
                description="手动调整当前结果",
                icon="✏️",
                shortcut="M"
            ))
        
        # Add abort option on later iterations
        if iteration > 0:
            options.append(FeedbackOption(
                id="abort",
                label="终止流程",
                action=FeedbackAction.ABORT,
                description="终止整个生成流程",
                icon="❌",
                shortcut="Esc",
                requires_confirmation=True
            ))
        
        return options
    
    async def get_auto_proceed_config(self, result: Any, task_context: TaskContext) -> Optional[AutoProceedConfig]:
        """Get auto-proceed configuration based on result confidence and user patterns."""
        
        if not self.orchestrator.config.enable_auto_proceed:
            return None
        
        # Get result confidence
        confidence = self.get_result_confidence(result)
        
        # Check if should auto-proceed based on adaptive strategy
        should_auto_proceed = self.adaptive_strategy.should_auto_proceed(
            stage=self.stage,
            confidence=confidence,
            user_preferences=self.orchestrator.feedback_manager.user_preferences,
            success_rate=self.orchestrator.feedback_manager.get_stage_success_rate(self.stage)
        )
        
        if not should_auto_proceed:
            return None
        
        # Calculate timeout based on user patterns
        timeout = self.adaptive_strategy.calculate_optimal_timeout(
            stage=self.stage,
            user_preferences=self.orchestrator.feedback_manager.user_preferences
        )
        
        return AutoProceedConfig(
            enabled=True,
            timeout_seconds=timeout,
            confidence_threshold=self.orchestrator.config.auto_proceed_confidence_threshold,
            condition=f"confidence >= {confidence:.2f}",
            allow_pause=True
        )
    
    async def process_feedback_response(self,
                                      response: FeedbackResponse,
                                      result: Any,
                                      task_context: TaskContext,
                                      input_data: Any) -> Any:
        """Process the feedback response and potentially modify the result."""
        # Default implementation returns result unchanged
        return result
    
    def adjust_parameters(self, input_data: Any, parameters: Dict[str, Any]) -> Any:
        """Adjust input parameters based on feedback."""
        # Default implementation returns input unchanged
        return input_data
    
    def apply_manual_adjustments(self, result: Any, adjustments: Dict[str, Any]) -> Any:
        """Apply manual adjustments to the result."""
        # Default implementation returns result unchanged
        return result
    
    def get_result_confidence(self, result: Any) -> float:
        """Get confidence score for the result."""
        # Default implementation returns neutral confidence
        return 0.5
    
    def supports_manual_adjustment(self) -> bool:
        """Check if this stage supports manual adjustment."""
        return False
    
    def get_stage_title(self) -> str:
        """Get display title for this stage."""
        stage_titles = {
            FeedbackStage.IMAGE_PREPROCESSING: "图像预处理",
            FeedbackStage.IMAGE_ANALYSIS: "图像分析",
            FeedbackStage.SPECIFICATION_GENERATION: "规格生成",
            FeedbackStage.CODE_GENERATION: "代码生成",
            FeedbackStage.MODEL_GENERATION: "模型生成",
            FeedbackStage.VISUAL_CRITIQUE: "视觉评估"
        }
        return stage_titles.get(self.stage, self.stage.value)
    
    def _is_feedback_enabled(self) -> bool:
        """Check if feedback is enabled for this stage."""
        return self.orchestrator.config.stage_feedback_enabled.get(self.stage.value, True)


class InteractiveOrchestratorAgent(OrchestratorAgent):
    """
    Enhanced orchestrator agent with interactive feedback capabilities.
    
    This orchestrator integrates user feedback collection at each stage
    of the workflow, allowing for dynamic adjustment and user control.
    """
    
    def __init__(self, config: InteractiveOrchestrationConfig):
        """Initialize the interactive orchestrator."""
        super().__init__(config)
        self.config = config
        
        # Initialize feedback manager
        self.feedback_manager = InteractiveFeedbackManager(
            interface_type=config.feedback_interface_type,
            config=config.__dict__
        )
        
        # Initialize adaptive strategy
        if config.enable_adaptive_learning:
            self.adaptive_strategy = AdaptiveStrategy(
                user_preference_weight=config.user_preference_weight,
                success_rate_weight=config.success_rate_weight,
                confidence_weight=config.confidence_weight
            )
        else:
            self.adaptive_strategy = None
        
        # Initialize stage executors
        self.stage_executors = self._initialize_stage_executors()
        
        logger.info("Interactive orchestrator agent initialized")
    
    def _initialize_stage_executors(self) -> Dict[FeedbackStage, StageExecutor]:
        """Initialize stage executors for each feedback stage."""
        from .stages import (
            ImagePreprocessingExecutor,
            ImageAnalysisExecutor,
            SpecificationGenerationExecutor,
            CodeGenerationExecutor,
            ModelGenerationExecutor,
            VisualCritiqueExecutor
        )
        
        return {
            FeedbackStage.IMAGE_PREPROCESSING: ImagePreprocessingExecutor(FeedbackStage.IMAGE_PREPROCESSING, self),
            FeedbackStage.IMAGE_ANALYSIS: ImageAnalysisExecutor(FeedbackStage.IMAGE_ANALYSIS, self),
            FeedbackStage.SPECIFICATION_GENERATION: SpecificationGenerationExecutor(FeedbackStage.SPECIFICATION_GENERATION, self),
            FeedbackStage.CODE_GENERATION: CodeGenerationExecutor(FeedbackStage.CODE_GENERATION, self),
            FeedbackStage.MODEL_GENERATION: ModelGenerationExecutor(FeedbackStage.MODEL_GENERATION, self),
            FeedbackStage.VISUAL_CRITIQUE: VisualCritiqueExecutor(FeedbackStage.VISUAL_CRITIQUE, self)
        }
    
    async def orchestrate_task_interactive(self,
                                         image_path: str,
                                         user_preferences: Dict[str, Any] = None,
                                         model_name: str = None,
                                         description: str = None) -> OrchestrationResult:
        """
        Orchestrate a complete 3D model generation task with interactive feedback.
        
        Args:
            image_path: Path to input image
            user_preferences: User preferences for generation
            model_name: Optional name for the model
            description: Optional description
            
        Returns:
            OrchestrationResult with final results and feedback history
        """
        # Create task context
        task_context = self._create_task_context(image_path, user_preferences, model_name, description)
        workflow_state = WorkflowState()
        
        logger.info(f"Starting interactive orchestration for task: {task_context.task_id}")
        
        try:
            # Execute stages with feedback
            processed_image = await self.stage_executors[FeedbackStage.IMAGE_PREPROCESSING].execute_with_feedback(
                task_context, workflow_state
            )
            
            analysis_result = await self.stage_executors[FeedbackStage.IMAGE_ANALYSIS].execute_with_feedback(
                task_context, workflow_state, processed_image
            )
            
            specification = await self.stage_executors[FeedbackStage.SPECIFICATION_GENERATION].execute_with_feedback(
                task_context, workflow_state, analysis_result
            )
            
            generated_code = await self.stage_executors[FeedbackStage.CODE_GENERATION].execute_with_feedback(
                task_context, workflow_state, specification
            )
            
            model_result = await self.stage_executors[FeedbackStage.MODEL_GENERATION].execute_with_feedback(
                task_context, workflow_state, generated_code
            )
            
            # Optional visual critique stage
            if self.config.enable_outer_loop:
                final_result = await self.stage_executors[FeedbackStage.VISUAL_CRITIQUE].execute_with_feedback(
                    task_context, workflow_state, model_result
                )
            else:
                final_result = model_result
            
            # Create result with feedback history
            result = OrchestrationResult(
                success=True,
                task_id=task_context.task_id,
                model_path=final_result.get('model_path'),
                render_path=final_result.get('render_path'),
                execution_time=workflow_state.get_total_execution_time(),
                stages_completed=workflow_state.stages_completed,
                feedback_history=self.feedback_manager.get_session_summary()
            )
            
            logger.info(f"Interactive orchestration completed successfully: {task_context.task_id}")
            return result
            
        except Exception as e:
            logger.error(f"Interactive orchestration failed: {e}")
            return OrchestrationResult(
                success=False,
                task_id=task_context.task_id,
                error=str(e),
                feedback_history=self.feedback_manager.get_session_summary()
            )
        finally:
            # Close feedback session
            await self.feedback_manager.close_session()
    
    def _create_task_context(self, image_path: str, user_preferences: Dict[str, Any], 
                           model_name: str, description: str) -> TaskContext:
        """Create task context for the orchestration."""
        import uuid
        from datetime import datetime
        
        task_id = str(uuid.uuid4())
        
        return TaskContext(
            task_id=task_id,
            image_path=image_path,
            user_preferences=user_preferences or {},
            model_name=model_name or f"Interactive_Model_{task_id[:8]}",
            description=description,
            created_at=datetime.now()
        )
