"""
Analytics and adaptive learning components for the interactive feedback system.

This module provides user preference learning, adaptive decision making,
and performance analytics for optimizing the feedback experience.
"""

import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import logging

from .core import FeedbackStage, FeedbackAction, FeedbackResponse

logger = logging.getLogger(__name__)


@dataclass
class UserPattern:
    """Represents a user behavior pattern."""
    stage: FeedbackStage
    action: FeedbackAction
    frequency: int = 0
    avg_response_time: float = 0.0
    confidence_scores: List[float] = field(default_factory=list)
    success_rate: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class PerformanceMetrics:
    """Performance metrics for a specific stage or overall system."""
    total_requests: int = 0
    successful_completions: int = 0
    user_satisfaction_score: float = 0.0
    avg_completion_time: float = 0.0
    retry_rate: float = 0.0
    abort_rate: float = 0.0
    auto_proceed_rate: float = 0.0
    manual_adjustment_rate: float = 0.0


class UserPreferenceManager:
    """Manages user preferences and learning from feedback patterns."""
    
    def __init__(self, user_id: Optional[str] = None, storage_path: Optional[Path] = None):
        self.user_id = user_id or "default_user"
        self.storage_path = storage_path or Path("user_data") / f"{self.user_id}_preferences.json"
        self.patterns: Dict[str, UserPattern] = {}
        self.global_preferences: Dict[str, Any] = {}
        self.load_preferences()
    
    def update_pattern(self, response: FeedbackResponse):
        """Update user patterns based on feedback response."""
        pattern_key = f"{response.stage.value}_{response.action.value}"
        
        if pattern_key not in self.patterns:
            self.patterns[pattern_key] = UserPattern(
                stage=response.stage,
                action=response.action
            )
        
        pattern = self.patterns[pattern_key]
        pattern.frequency += 1
        pattern.last_updated = datetime.now()
        
        # Update response time
        if response.response_time_seconds:
            if pattern.avg_response_time == 0:
                pattern.avg_response_time = response.response_time_seconds
            else:
                # Exponential moving average
                alpha = 0.3
                pattern.avg_response_time = (
                    alpha * response.response_time_seconds + 
                    (1 - alpha) * pattern.avg_response_time
                )
        
        # Update confidence scores
        if response.confidence:
            pattern.confidence_scores.append(response.confidence)
            # Keep only last 10 scores
            pattern.confidence_scores = pattern.confidence_scores[-10:]
        
        self.save_preferences()
    
    def get_preferred_action(self, stage: FeedbackStage) -> Optional[FeedbackAction]:
        """Get the user's preferred action for a stage."""
        stage_patterns = [p for p in self.patterns.values() if p.stage == stage]
        
        if not stage_patterns:
            return None
        
        # Return the most frequent action
        return max(stage_patterns, key=lambda p: p.frequency).action
    
    def get_avg_response_time(self, stage: FeedbackStage) -> float:
        """Get average response time for a stage."""
        stage_patterns = [p for p in self.patterns.values() if p.stage == stage]
        
        if not stage_patterns:
            return 30.0  # Default 30 seconds
        
        times = [p.avg_response_time for p in stage_patterns if p.avg_response_time > 0]
        return np.mean(times) if times else 30.0
    
    def get_confidence_threshold(self, stage: FeedbackStage) -> float:
        """Get confidence threshold for auto-proceed decisions."""
        stage_patterns = [p for p in self.patterns.values() if p.stage == stage]
        
        if not stage_patterns:
            return 0.8  # Default threshold
        
        # Calculate based on historical confidence scores
        all_scores = []
        for pattern in stage_patterns:
            all_scores.extend(pattern.confidence_scores)
        
        if not all_scores:
            return 0.8
        
        # Use 75th percentile as threshold
        return np.percentile(all_scores, 75)
    
    def save_preferences(self):
        """Save preferences to storage."""
        try:
            self.storage_path.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'user_id': self.user_id,
                'last_updated': datetime.now().isoformat(),
                'global_preferences': self.global_preferences,
                'patterns': {}
            }
            
            # Serialize patterns
            for key, pattern in self.patterns.items():
                data['patterns'][key] = {
                    'stage': pattern.stage.value,
                    'action': pattern.action.value,
                    'frequency': pattern.frequency,
                    'avg_response_time': pattern.avg_response_time,
                    'confidence_scores': pattern.confidence_scores,
                    'success_rate': pattern.success_rate,
                    'last_updated': pattern.last_updated.isoformat()
                }
            
            with open(self.storage_path, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save user preferences: {e}")
    
    def load_preferences(self):
        """Load preferences from storage."""
        try:
            if not self.storage_path.exists():
                return
            
            with open(self.storage_path, 'r') as f:
                data = json.load(f)
            
            self.global_preferences = data.get('global_preferences', {})
            
            # Deserialize patterns
            for key, pattern_data in data.get('patterns', {}).items():
                self.patterns[key] = UserPattern(
                    stage=FeedbackStage(pattern_data['stage']),
                    action=FeedbackAction(pattern_data['action']),
                    frequency=pattern_data['frequency'],
                    avg_response_time=pattern_data['avg_response_time'],
                    confidence_scores=pattern_data['confidence_scores'],
                    success_rate=pattern_data['success_rate'],
                    last_updated=datetime.fromisoformat(pattern_data['last_updated'])
                )
                
        except Exception as e:
            logger.error(f"Failed to load user preferences: {e}")


class AdaptiveStrategy:
    """Adaptive strategy for making intelligent decisions about auto-proceed and timeouts."""
    
    def __init__(self, 
                 user_preference_weight: float = 0.3,
                 success_rate_weight: float = 0.4,
                 confidence_weight: float = 0.3):
        self.user_preference_weight = user_preference_weight
        self.success_rate_weight = success_rate_weight
        self.confidence_weight = confidence_weight
        self.preference_manager = UserPreferenceManager()
    
    def should_auto_proceed(self,
                          stage: FeedbackStage,
                          confidence: float,
                          user_preferences: Dict[str, Any],
                          success_rate: float) -> bool:
        """
        Determine if the system should auto-proceed based on multiple factors.
        
        Args:
            stage: Current workflow stage
            confidence: Confidence score of the result
            user_preferences: User preference data
            success_rate: Historical success rate for this stage
            
        Returns:
            True if should auto-proceed, False otherwise
        """
        # Get user preference score
        preferred_action = self.preference_manager.get_preferred_action(stage)
        user_score = 1.0 if preferred_action == FeedbackAction.PROCEED else 0.0
        
        # Get confidence threshold
        confidence_threshold = self.preference_manager.get_confidence_threshold(stage)
        confidence_score = 1.0 if confidence >= confidence_threshold else 0.0
        
        # Calculate weighted score
        total_score = (
            self.user_preference_weight * user_score +
            self.success_rate_weight * success_rate +
            self.confidence_weight * confidence_score
        )
        
        # Auto-proceed if score is above threshold
        return total_score > 0.7
    
    def calculate_optimal_timeout(self,
                                stage: FeedbackStage,
                                user_preferences: Dict[str, Any]) -> int:
        """
        Calculate optimal timeout based on user patterns.
        
        Args:
            stage: Current workflow stage
            user_preferences: User preference data
            
        Returns:
            Optimal timeout in seconds
        """
        # Get average response time for this stage
        avg_response_time = self.preference_manager.get_avg_response_time(stage)
        
        # Add buffer (2x average response time, min 15s, max 120s)
        optimal_timeout = max(15, min(120, int(avg_response_time * 2)))
        
        return optimal_timeout
    
    def suggest_parameters(self,
                         stage: FeedbackStage,
                         failure_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Suggest parameter adjustments based on failure history.
        
        Args:
            stage: Current workflow stage
            failure_history: List of previous failures
            
        Returns:
            Dictionary of suggested parameter adjustments
        """
        suggestions = {}
        
        # Analyze failure patterns
        if len(failure_history) >= 2:
            # If multiple failures, suggest more conservative parameters
            if stage == FeedbackStage.IMAGE_ANALYSIS:
                suggestions['granularity'] = 'COMPREHENSIVE'
                suggestions['confidence_threshold'] = 0.9
            elif stage == FeedbackStage.SPECIFICATION_GENERATION:
                suggestions['quality_level'] = 'high'
                suggestions['validation_strict'] = True
            elif stage == FeedbackStage.CODE_GENERATION:
                suggestions['safety_checks'] = True
                suggestions['optimization_level'] = 'conservative'
        
        return suggestions


class FeedbackAnalytics:
    """Analytics system for tracking and analyzing feedback patterns."""
    
    def __init__(self, storage_path: Optional[Path] = None):
        self.storage_path = storage_path or Path("analytics") / "feedback_analytics.json"
        self.metrics: Dict[str, PerformanceMetrics] = {}
        self.session_data: List[Dict[str, Any]] = []
        self.load_analytics()
    
    def record_session(self, session_summary: Dict[str, Any]):
        """Record a completed feedback session."""
        self.session_data.append({
            **session_summary,
            'recorded_at': datetime.now().isoformat()
        })
        
        # Update metrics
        self._update_metrics(session_summary)
        self.save_analytics()
    
    def _update_metrics(self, session_summary: Dict[str, Any]):
        """Update performance metrics based on session data."""
        stages_covered = session_summary.get('stages_covered', [])
        
        for stage in stages_covered:
            if stage not in self.metrics:
                self.metrics[stage] = PerformanceMetrics()
            
            metrics = self.metrics[stage]
            metrics.total_requests += 1
            
            # Analyze session for success indicators
            # This is a simplified analysis - in practice, you'd want more sophisticated metrics
            if session_summary.get('success_rate', 0) > 0.8:
                metrics.successful_completions += 1
    
    def get_stage_analytics(self, stage: FeedbackStage) -> Dict[str, Any]:
        """Get analytics for a specific stage."""
        stage_key = stage.value
        metrics = self.metrics.get(stage_key, PerformanceMetrics())
        
        return {
            'stage': stage_key,
            'total_requests': metrics.total_requests,
            'success_rate': metrics.successful_completions / max(1, metrics.total_requests),
            'avg_completion_time': metrics.avg_completion_time,
            'retry_rate': metrics.retry_rate,
            'auto_proceed_rate': metrics.auto_proceed_rate,
            'user_satisfaction': metrics.user_satisfaction_score
        }
    
    def get_overall_analytics(self) -> Dict[str, Any]:
        """Get overall system analytics."""
        total_sessions = len(self.session_data)
        
        if total_sessions == 0:
            return {'total_sessions': 0}
        
        # Calculate overall metrics
        recent_sessions = self.session_data[-100:]  # Last 100 sessions
        
        avg_satisfaction = np.mean([
            s.get('user_satisfaction', 0.5) for s in recent_sessions
        ])
        
        avg_completion_time = np.mean([
            s.get('total_time', 0) for s in recent_sessions
        ])
        
        return {
            'total_sessions': total_sessions,
            'avg_user_satisfaction': avg_satisfaction,
            'avg_completion_time': avg_completion_time,
            'stage_analytics': {
                stage: self.get_stage_analytics(FeedbackStage(stage))
                for stage in FeedbackStage
            }
        }
    
    def generate_insights(self) -> List[str]:
        """Generate actionable insights from analytics data."""
        insights = []
        
        overall = self.get_overall_analytics()
        
        # User satisfaction insights
        if overall.get('avg_user_satisfaction', 0) < 0.7:
            insights.append("用户满意度较低，建议优化用户体验")
        
        # Performance insights
        if overall.get('avg_completion_time', 0) > 300:  # 5 minutes
            insights.append("平均完成时间较长，建议优化处理速度")
        
        # Stage-specific insights
        for stage_name, stage_analytics in overall.get('stage_analytics', {}).items():
            if stage_analytics['retry_rate'] > 0.3:
                insights.append(f"{stage_name}阶段重试率较高，建议改进算法")
        
        return insights
    
    def save_analytics(self):
        """Save analytics data to storage."""
        try:
            self.storage_path.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'last_updated': datetime.now().isoformat(),
                'metrics': {},
                'session_data': self.session_data[-1000:]  # Keep last 1000 sessions
            }
            
            # Serialize metrics
            for stage, metrics in self.metrics.items():
                data['metrics'][stage] = {
                    'total_requests': metrics.total_requests,
                    'successful_completions': metrics.successful_completions,
                    'user_satisfaction_score': metrics.user_satisfaction_score,
                    'avg_completion_time': metrics.avg_completion_time,
                    'retry_rate': metrics.retry_rate,
                    'abort_rate': metrics.abort_rate,
                    'auto_proceed_rate': metrics.auto_proceed_rate,
                    'manual_adjustment_rate': metrics.manual_adjustment_rate
                }
            
            with open(self.storage_path, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save analytics: {e}")
    
    def load_analytics(self):
        """Load analytics data from storage."""
        try:
            if not self.storage_path.exists():
                return
            
            with open(self.storage_path, 'r') as f:
                data = json.load(f)
            
            self.session_data = data.get('session_data', [])
            
            # Deserialize metrics
            for stage, metrics_data in data.get('metrics', {}).items():
                self.metrics[stage] = PerformanceMetrics(**metrics_data)
                
        except Exception as e:
            logger.error(f"Failed to load analytics: {e}")
