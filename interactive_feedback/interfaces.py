"""
Feedback interfaces for different interaction modes.

This module provides various interfaces for collecting user feedback,
including Web UI, Desktop Application, and CLI interfaces.
"""

import asyncio
import json
import webbrowser
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from pathlib import Path
import logging

from .core import FeedbackRequest, FeedbackResponse, InteractiveFeedbackManager

logger = logging.getLogger(__name__)


class FeedbackInterface(ABC):
    """Abstract base class for feedback interfaces."""
    
    def __init__(self, session_id: str, config: Dict[str, Any], feedback_manager: InteractiveFeedbackManager):
        self.session_id = session_id
        self.config = config
        self.feedback_manager = feedback_manager
        self.is_active = False
    
    @abstractmethod
    async def display_feedback_request(self, request: FeedbackRequest):
        """Display a feedback request to the user."""
        pass
    
    @abstractmethod
    async def close(self):
        """Close the interface and cleanup resources."""
        pass


class WebFeedbackInterface(FeedbackInterface):
    """Web-based feedback interface using WebSocket communication."""
    
    def __init__(self, session_id: str, config: Dict[str, Any], feedback_manager: InteractiveFeedbackManager):
        super().__init__(session_id, config, feedback_manager)
        self.server = None
        self.websocket_connection = None
        self.host = config.get('web_host', '127.0.0.1')
        self.port = config.get('web_port', 8765)
    
    async def initialize(self):
        """Initialize the web interface."""
        try:
            # Start the web server
            from .server import BlenderAIFeedbackServer
            
            self.server = BlenderAIFeedbackServer(
                host=self.host,
                port=self.port,
                feedback_manager=self.feedback_manager
            )
            
            # Start server in background
            await self.server.start()
            
            # Open browser
            url = f"http://{self.host}:{self.port}?session={self.session_id}"
            webbrowser.open(url)
            
            self.is_active = True
            logger.info(f"Web feedback interface started at {url}")
            
        except Exception as e:
            logger.error(f"Failed to initialize web interface: {e}")
            raise
    
    async def display_feedback_request(self, request: FeedbackRequest):
        """Send feedback request to web interface via WebSocket."""
        if not self.is_active:
            await self.initialize()
        
        # Send request to web interface
        await self.server.broadcast_to_session(self.session_id, {
            "type": "feedback_request",
            "request": self._serialize_request(request)
        })
        
        logger.info(f"Sent feedback request to web interface: {request.stage.value}")
    
    def _serialize_request(self, request: FeedbackRequest) -> Dict[str, Any]:
        """Serialize feedback request for JSON transmission."""
        return {
            "request_id": request.request_id,
            "session_id": request.session_id,
            "stage": request.stage.value,
            "title": request.title,
            "content": request.content,
            "options": [
                {
                    "id": opt.id,
                    "label": opt.label,
                    "action": opt.action.value,
                    "description": opt.description,
                    "icon": opt.icon,
                    "shortcut": opt.shortcut,
                    "requires_confirmation": opt.requires_confirmation
                }
                for opt in request.options
            ],
            "auto_proceed": {
                "enabled": request.auto_proceed.enabled,
                "timeout_seconds": request.auto_proceed.timeout_seconds,
                "confidence_threshold": request.auto_proceed.confidence_threshold,
                "condition": request.auto_proceed.condition,
                "allow_pause": request.auto_proceed.allow_pause
            } if request.auto_proceed else None,
            "timestamp": request.timestamp.isoformat()
        }
    
    async def close(self):
        """Close the web interface."""
        if self.server:
            await self.server.stop()
        self.is_active = False
        logger.info("Web feedback interface closed")


class DesktopFeedbackInterface(FeedbackInterface):
    """Desktop application feedback interface using Tauri."""
    
    def __init__(self, session_id: str, config: Dict[str, Any], feedback_manager: InteractiveFeedbackManager):
        super().__init__(session_id, config, feedback_manager)
        self.app_process = None
    
    async def initialize(self):
        """Initialize the desktop application."""
        try:
            import subprocess
            import sys
            
            # Path to desktop application
            app_path = Path(__file__).parent.parent / "desktop_app" / "target" / "release"
            
            if sys.platform == "win32":
                app_executable = app_path / "blender-ai-feedback.exe"
            elif sys.platform == "darwin":
                app_executable = app_path / "blender-ai-feedback.app" / "Contents" / "MacOS" / "blender-ai-feedback"
            else:
                app_executable = app_path / "blender-ai-feedback"
            
            if not app_executable.exists():
                raise FileNotFoundError(f"Desktop application not found: {app_executable}")
            
            # Launch desktop application
            self.app_process = subprocess.Popen([
                str(app_executable),
                "--session-id", self.session_id,
                "--backend-port", str(self.config.get('web_port', 8765))
            ])
            
            self.is_active = True
            logger.info("Desktop feedback interface launched")
            
        except Exception as e:
            logger.error(f"Failed to initialize desktop interface: {e}")
            raise
    
    async def display_feedback_request(self, request: FeedbackRequest):
        """Send feedback request to desktop application."""
        if not self.is_active:
            await self.initialize()
        
        # Desktop app communicates via the same WebSocket server as web interface
        # The desktop app will connect to the WebSocket and receive the same messages
        from .server import BlenderAIFeedbackServer
        
        if not hasattr(self, 'server'):
            self.server = BlenderAIFeedbackServer(
                host='127.0.0.1',
                port=self.config.get('web_port', 8765),
                feedback_manager=self.feedback_manager
            )
            await self.server.start()
        
        await self.server.broadcast_to_session(self.session_id, {
            "type": "feedback_request",
            "request": self._serialize_request(request)
        })
        
        logger.info(f"Sent feedback request to desktop interface: {request.stage.value}")
    
    def _serialize_request(self, request: FeedbackRequest) -> Dict[str, Any]:
        """Serialize feedback request for desktop application."""
        # Same serialization as web interface
        return WebFeedbackInterface._serialize_request(self, request)
    
    async def close(self):
        """Close the desktop application."""
        if self.app_process:
            self.app_process.terminate()
            try:
                self.app_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.app_process.kill()
        
        if hasattr(self, 'server'):
            await self.server.stop()
        
        self.is_active = False
        logger.info("Desktop feedback interface closed")


class CLIFeedbackInterface(FeedbackInterface):
    """Command-line feedback interface for terminal environments."""
    
    def __init__(self, session_id: str, config: Dict[str, Any], feedback_manager: InteractiveFeedbackManager):
        super().__init__(session_id, config, feedback_manager)
        self.is_active = True
    
    async def display_feedback_request(self, request: FeedbackRequest):
        """Display feedback request in terminal."""
        print("\n" + "="*60)
        print(f"🎯 {request.title}")
        print("="*60)
        
        # Display content
        await self._display_content(request.content, request.stage.value)
        
        # Display options
        print("\n📋 可用选项:")
        for i, option in enumerate(request.options, 1):
            shortcut = f" ({option.shortcut})" if option.shortcut else ""
            print(f"  {i}. {option.label}{shortcut}")
            if option.description:
                print(f"     {option.description}")
        
        # Auto-proceed info
        if request.auto_proceed and request.auto_proceed.enabled:
            print(f"\n⏰ 自动继续: {request.auto_proceed.timeout_seconds}秒后自动选择选项1")
            print("   按任意键暂停自动继续")
        
        # Get user input
        response = await self._get_user_input(request)
        
        # Submit response
        await self.feedback_manager.submit_feedback_response(response)
    
    async def _display_content(self, content: Dict[str, Any], stage: str):
        """Display stage-specific content."""
        if stage == "image_preprocessing":
            print(f"📸 原始图像: {content.get('original_image', 'N/A')}")
            print(f"🔧 处理后图像: {content.get('processed_image', 'N/A')}")
            if 'processing_info' in content:
                info = content['processing_info']
                print(f"📏 分辨率: {info.get('resolution', 'N/A')}")
                print(f"🎨 格式: {info.get('format', 'N/A')}")
        
        elif stage == "image_analysis":
            print("🔍 检测到的对象:")
            for obj in content.get('detected_objects', []):
                print(f"  - {obj.get('type', 'Unknown')}: 置信度 {obj.get('confidence', 0):.2f}")
            
            if 'scene_properties' in content:
                props = content['scene_properties']
                print(f"💡 光照: {props.get('lighting', 'N/A')}")
                print(f"🎨 风格: {props.get('style', 'N/A')}")
                print(f"🔧 复杂度: {props.get('complexity', 'N/A')}")
        
        elif stage == "specification_generation":
            spec = content.get('specification', {})
            print(f"📦 对象数量: {len(spec.get('objects', []))}")
            print(f"🎨 材质数量: {len(spec.get('materials', []))}")
            print(f"📊 预估复杂度: {content.get('estimated_complexity', 'N/A')}")
        
        elif stage == "code_generation":
            print(f"📝 生成的代码行数: {content.get('code_lines', 'N/A')}")
            print(f"🔧 使用的函数: {len(content.get('functions_used', []))}")
            print(f"✅ 语法检查: {'通过' if content.get('syntax_valid') else '失败'}")
        
        elif stage == "model_generation":
            print(f"🎯 模型文件: {content.get('model_path', 'N/A')}")
            print(f"🖼️ 渲染图: {content.get('render_path', 'N/A')}")
            print(f"⏱️ 生成时间: {content.get('generation_time', 'N/A')}秒")
    
    async def _get_user_input(self, request: FeedbackRequest) -> FeedbackResponse:
        """Get user input for feedback response."""
        from .core import FeedbackAction
        
        # Setup auto-proceed if enabled
        auto_proceed_task = None
        if request.auto_proceed and request.auto_proceed.enabled:
            auto_proceed_task = asyncio.create_task(
                asyncio.sleep(request.auto_proceed.timeout_seconds)
            )
        
        # Get user input
        input_task = asyncio.create_task(self._async_input("\n👉 请选择 (输入数字): "))
        
        # Wait for either user input or auto-proceed timeout
        if auto_proceed_task:
            done, pending = await asyncio.wait(
                [input_task, auto_proceed_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Cancel pending tasks
            for task in pending:
                task.cancel()
            
            if auto_proceed_task in done:
                # Auto-proceed triggered
                print("\n⏰ 自动继续...")
                choice = 1
            else:
                # User input received
                choice = input_task.result()
        else:
            choice = await input_task
        
        # Validate choice
        try:
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(request.options):
                selected_option = request.options[choice_idx]
            else:
                # Default to first option
                selected_option = request.options[0]
        except (ValueError, IndexError):
            # Default to first option
            selected_option = request.options[0]
        
        return FeedbackResponse(
            request_id=request.request_id,
            session_id=request.session_id,
            stage=request.stage,
            action=selected_option.action
        )
    
    async def _async_input(self, prompt: str) -> str:
        """Asynchronous input function."""
        import sys
        
        # Use asyncio's run_in_executor for non-blocking input
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, input, prompt)
    
    async def close(self):
        """Close the CLI interface."""
        print("\n👋 反馈会话结束")
        self.is_active = False


class FeedbackInterfaceFactory:
    """Factory for creating feedback interfaces."""
    
    @staticmethod
    async def create_interface(interface_type: str,
                             session_id: str,
                             config: Dict[str, Any],
                             feedback_manager: InteractiveFeedbackManager) -> FeedbackInterface:
        """
        Create a feedback interface of the specified type.
        
        Args:
            interface_type: Type of interface ("web", "desktop", "cli")
            session_id: Session ID for the interface
            config: Configuration dictionary
            feedback_manager: Feedback manager instance
            
        Returns:
            Initialized feedback interface
        """
        if interface_type == "web":
            interface = WebFeedbackInterface(session_id, config, feedback_manager)
        elif interface_type == "desktop":
            interface = DesktopFeedbackInterface(session_id, config, feedback_manager)
        elif interface_type == "cli":
            interface = CLIFeedbackInterface(session_id, config, feedback_manager)
        else:
            raise ValueError(f"Unknown interface type: {interface_type}")
        
        # Initialize if needed
        if hasattr(interface, 'initialize'):
            await interface.initialize()
        
        return interface
