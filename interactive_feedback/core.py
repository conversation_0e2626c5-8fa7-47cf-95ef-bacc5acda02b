"""
Core components for the Interactive Feedback System.

This module defines the fundamental data structures and the main
InteractiveFeedbackManager class that coordinates user feedback collection.
"""

import uuid
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class FeedbackAction(Enum):
    """Available feedback actions."""
    PROCEED = "proceed"
    RETRY = "retry"
    MANUAL_ADJUST = "manual_adjust"
    SKIP = "skip"
    ABORT = "abort"
    CUSTOM = "custom"


class FeedbackStage(Enum):
    """Workflow stages that support feedback."""
    IMAGE_PREPROCESSING = "image_preprocessing"
    IMAGE_ANALYSIS = "image_analysis"
    SPECIFICATION_GENERATION = "specification_generation"
    CODE_GENERATION = "code_generation"
    MODEL_GENERATION = "model_generation"
    VISUAL_CRITIQUE = "visual_critique"


@dataclass
class FeedbackOption:
    """Represents a feedback option presented to the user."""
    id: str
    label: str
    action: FeedbackAction
    description: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    icon: Optional[str] = None
    shortcut: Optional[str] = None
    requires_confirmation: bool = False


@dataclass
class AutoProceedConfig:
    """Configuration for automatic proceeding."""
    enabled: bool = True
    timeout_seconds: int = 30
    confidence_threshold: float = 0.8
    condition: Optional[str] = None
    allow_pause: bool = True


@dataclass
class FeedbackRequest:
    """Request for user feedback at a specific stage."""
    session_id: str
    stage: FeedbackStage
    title: str
    content: Dict[str, Any]
    options: List[FeedbackOption]
    auto_proceed: Optional[AutoProceedConfig] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    request_id: str = field(default_factory=lambda: str(uuid.uuid4()))


@dataclass
class FeedbackResponse:
    """User's response to a feedback request."""
    request_id: str
    session_id: str
    stage: FeedbackStage
    action: FeedbackAction
    parameters: Dict[str, Any] = field(default_factory=dict)
    user_input: Optional[str] = None
    confidence: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)
    response_time_seconds: Optional[float] = None


@dataclass
class FeedbackSession:
    """Represents a complete feedback session."""
    session_id: str
    user_id: Optional[str] = None
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    requests: List[FeedbackRequest] = field(default_factory=list)
    responses: List[FeedbackResponse] = field(default_factory=list)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


class InteractiveFeedbackManager:
    """
    Main manager for interactive feedback collection and processing.
    
    This class coordinates the feedback collection process across different
    interfaces and manages user preferences and session data.
    """
    
    def __init__(self, 
                 interface_type: str = "web",
                 session_id: Optional[str] = None,
                 config: Optional[Dict[str, Any]] = None):
        """
        Initialize the feedback manager.
        
        Args:
            interface_type: Type of interface ("web", "desktop", "cli")
            session_id: Optional session ID (generates new if not provided)
            config: Optional configuration dictionary
        """
        self.interface_type = interface_type
        self.session_id = session_id or str(uuid.uuid4())
        self.config = config or {}
        
        # Session management
        self.current_session = FeedbackSession(session_id=self.session_id)
        self.feedback_interface = None
        self.pending_requests: Dict[str, FeedbackRequest] = {}
        self.response_futures: Dict[str, asyncio.Future] = {}
        
        # User preferences and learning
        self.user_preferences = {}
        self.stage_patterns = {}
        self.success_metrics = {}
        
        logger.info(f"Interactive feedback manager initialized: {self.session_id}")
    
    async def request_feedback(self,
                             stage: FeedbackStage,
                             title: str,
                             content: Dict[str, Any],
                             options: List[FeedbackOption],
                             auto_proceed: Optional[AutoProceedConfig] = None,
                             timeout: Optional[float] = None) -> FeedbackResponse:
        """
        Request feedback from the user at a specific stage.
        
        Args:
            stage: The workflow stage requesting feedback
            title: Display title for the feedback request
            content: Content to display to the user
            options: Available feedback options
            auto_proceed: Auto-proceed configuration
            timeout: Optional timeout in seconds
            
        Returns:
            FeedbackResponse from the user
        """
        # Create feedback request
        request = FeedbackRequest(
            session_id=self.session_id,
            stage=stage,
            title=title,
            content=content,
            options=options,
            auto_proceed=auto_proceed
        )
        
        # Add to session
        self.current_session.requests.append(request)
        self.pending_requests[request.request_id] = request
        
        logger.info(f"Requesting feedback for stage: {stage.value}")
        
        try:
            # Initialize interface if needed
            if not self.feedback_interface:
                await self._initialize_interface()
            
            # Create response future
            response_future = asyncio.Future()
            self.response_futures[request.request_id] = response_future
            
            # Send request to interface
            await self.feedback_interface.display_feedback_request(request)
            
            # Wait for response with timeout
            response_timeout = timeout or self.config.get('default_timeout', 300)
            
            try:
                response = await asyncio.wait_for(response_future, timeout=response_timeout)
            except asyncio.TimeoutError:
                logger.warning(f"Feedback request timed out for stage: {stage.value}")
                # Create default response
                response = FeedbackResponse(
                    request_id=request.request_id,
                    session_id=self.session_id,
                    stage=stage,
                    action=FeedbackAction.PROCEED,
                    parameters={"timeout": True}
                )
            
            # Process response
            await self._process_feedback_response(response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error requesting feedback: {e}")
            # Return default proceed response on error
            return FeedbackResponse(
                request_id=request.request_id,
                session_id=self.session_id,
                stage=stage,
                action=FeedbackAction.PROCEED,
                parameters={"error": str(e)}
            )
        finally:
            # Cleanup
            self.pending_requests.pop(request.request_id, None)
            self.response_futures.pop(request.request_id, None)
    
    async def submit_feedback_response(self, response: FeedbackResponse):
        """
        Submit a feedback response (called by interface).
        
        Args:
            response: The feedback response from the user
        """
        request_id = response.request_id
        
        if request_id in self.response_futures:
            future = self.response_futures[request_id]
            if not future.done():
                future.set_result(response)
        else:
            logger.warning(f"Received response for unknown request: {request_id}")
    
    async def _initialize_interface(self):
        """Initialize the feedback interface based on type."""
        from .interfaces import FeedbackInterfaceFactory
        
        self.feedback_interface = await FeedbackInterfaceFactory.create_interface(
            interface_type=self.interface_type,
            session_id=self.session_id,
            config=self.config,
            feedback_manager=self
        )
        
        logger.info(f"Initialized {self.interface_type} feedback interface")
    
    async def _process_feedback_response(self, response: FeedbackResponse):
        """Process and learn from feedback response."""
        # Add to session
        self.current_session.responses.append(response)
        
        # Update user preferences
        self._update_user_preferences(response)
        
        # Update success metrics
        self._update_success_metrics(response)
        
        logger.info(f"Processed feedback response: {response.action.value}")
    
    def _update_user_preferences(self, response: FeedbackResponse):
        """Update user preferences based on response."""
        stage = response.stage.value
        action = response.action.value
        
        if stage not in self.user_preferences:
            self.user_preferences[stage] = {}
        
        if action not in self.user_preferences[stage]:
            self.user_preferences[stage][action] = 0
        
        self.user_preferences[stage][action] += 1
        
        # Store response time patterns
        if response.response_time_seconds:
            if 'response_times' not in self.user_preferences[stage]:
                self.user_preferences[stage]['response_times'] = []
            
            self.user_preferences[stage]['response_times'].append(response.response_time_seconds)
    
    def _update_success_metrics(self, response: FeedbackResponse):
        """Update success metrics for adaptive learning."""
        stage = response.stage.value
        
        if stage not in self.success_metrics:
            self.success_metrics[stage] = {
                'total_requests': 0,
                'proceed_count': 0,
                'retry_count': 0,
                'manual_adjust_count': 0
            }
        
        metrics = self.success_metrics[stage]
        metrics['total_requests'] += 1
        
        if response.action == FeedbackAction.PROCEED:
            metrics['proceed_count'] += 1
        elif response.action == FeedbackAction.RETRY:
            metrics['retry_count'] += 1
        elif response.action == FeedbackAction.MANUAL_ADJUST:
            metrics['manual_adjust_count'] += 1
    
    def get_user_preference_score(self, stage: FeedbackStage, action: FeedbackAction) -> float:
        """Get user preference score for a stage-action combination."""
        stage_prefs = self.user_preferences.get(stage.value, {})
        action_count = stage_prefs.get(action.value, 0)
        total_count = sum(stage_prefs.values()) if stage_prefs else 0
        
        if total_count == 0:
            return 0.5  # Neutral preference
        
        return action_count / total_count
    
    def get_stage_success_rate(self, stage: FeedbackStage) -> float:
        """Get success rate for a stage (proceed rate)."""
        metrics = self.success_metrics.get(stage.value, {})
        total = metrics.get('total_requests', 0)
        proceed = metrics.get('proceed_count', 0)
        
        if total == 0:
            return 0.5  # Neutral success rate
        
        return proceed / total
    
    async def close_session(self):
        """Close the current feedback session."""
        self.current_session.end_time = datetime.now()
        
        if self.feedback_interface:
            await self.feedback_interface.close()
        
        logger.info(f"Closed feedback session: {self.session_id}")
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get a summary of the current session."""
        session = self.current_session
        
        return {
            'session_id': session.session_id,
            'start_time': session.start_time.isoformat(),
            'end_time': session.end_time.isoformat() if session.end_time else None,
            'total_requests': len(session.requests),
            'total_responses': len(session.responses),
            'stages_covered': list(set(req.stage.value for req in session.requests)),
            'user_preferences': self.user_preferences,
            'success_metrics': self.success_metrics
        }
