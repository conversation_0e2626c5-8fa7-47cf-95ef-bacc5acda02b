# Multi-LLM Provider Support Dependencies
# Additional requirements for supporting multiple LLM providers

# Core LLM providers
openai>=1.0.0                    # OpenAI GPT models
google-generativeai>=0.3.0      # Google Gemini models (optional)

# HTTP clients and utilities
httpx>=0.25.0                    # Modern HTTP client
requests>=2.31.0                 # HTTP library
aiohttp>=3.8.0                   # Async HTTP client

# Configuration and environment
python-dotenv>=1.0.0             # Environment variable management
pydantic>=2.0.0                  # Data validation (already in main requirements)

# Monitoring and metrics
psutil>=5.9.0                    # System monitoring (already in main requirements)

# Optional dependencies for specific providers
# Install these based on your needs:

# For Gemini support:
# google-generativeai>=0.3.0
# google-auth>=2.0.0
# google-auth-oauthlib>=1.0.0

# For advanced monitoring:
# prometheus-client>=0.17.0
# grafana-api>=1.0.0

# For async support:
# asyncio>=3.4.3
# aiofiles>=23.0.0

# Development and testing
pytest-asyncio>=0.21.0           # Async testing support
