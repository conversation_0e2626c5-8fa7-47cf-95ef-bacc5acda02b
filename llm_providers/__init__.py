"""
Multi-LLM Provider Support for Blender AI Agent System

This module provides a unified interface for multiple LLM providers including
OpenAI, Google Gemini, DeepSeek, Kimi, and other popular models.

Features:
- Unified LLM interface
- Provider-agnostic configuration
- Automatic fallback mechanisms
- Cost optimization
- Performance monitoring

Author: Augment Agent
Date: 2025-07-19
Version: 1.0.0
"""

from .base import LLMProvider, LLMConfig, LLMResponse, LLMError
from .openai_provider import OpenAIProvider
from .gemini_provider import GeminiProvider
from .deepseek_provider import DeepSeekProvider
from .kimi_provider import KimiProvider
from .factory import LLMFactory, create_llm_provider
from .manager import LLMManager

__all__ = [
    'LLMProvider',
    'LLMConfig', 
    'LLMResponse',
    'LLMError',
    'OpenAIProvider',
    'GeminiProvider',
    'DeepSeekProvider',
    'KimiProvider',
    'LLMFactory',
    'LLMManager',
    'create_llm_provider'
]

__version__ = "1.0.0"
