"""
DeepSeek provider implementation.

This module implements the DeepSeek LLM provider with support for
DeepSeek models via OpenAI-compatible API.
"""

from typing import List, Dict, Any, Optional
import logging
import requests

from .openai_provider import OpenAIProvider
from .base import LLMConfig, LLMError, LLMProviderType

logger = logging.getLogger(__name__)


class DeepSeekProvider(OpenAIProvider):
    """DeepSeek LLM provider implementation using OpenAI-compatible API."""
    
    # DeepSeek model pricing (per 1M tokens) - Updated as of 2025
    MODEL_PRICING = {
        "deepseek-chat": {"input": 0.14, "output": 0.28},
        "deepseek-coder": {"input": 0.14, "output": 0.28},
        "deepseek-v2.5": {"input": 0.14, "output": 0.28},
        "deepseek-v3": {"input": 0.27, "output": 1.1},  # Latest model
    }
    
    DEFAULT_BASE_URL = "https://api.deepseek.com/v1"
    
    def __init__(self, config: LLMConfig):
        # Set default base URL for DeepSeek
        if not config.base_url:
            config.base_url = self.DEFAULT_BASE_URL
        
        # Override provider type
        config.provider = LLMProviderType.DEEPSEEK
        
        super().__init__(config)
    
    def _initialize_client(self):
        """Initialize DeepSeek client using OpenAI-compatible interface."""
        try:
            from openai import OpenAI
            
            self.client = OpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url,
                timeout=self.config.timeout
            )
            logger.info(f"DeepSeek client initialized with model: {self.config.model}")
            
        except Exception as e:
            raise LLMError(f"Failed to initialize DeepSeek client: {e}", provider=LLMProviderType.DEEPSEEK)
    
    def supports_multimodal(self) -> bool:
        """DeepSeek currently doesn't support multimodal input."""
        return False
    
    def supports_function_calling(self) -> bool:
        """Check if current model supports function calling."""
        # DeepSeek models support function calling
        return True
    
    def estimate_cost(self, prompt_tokens: int, completion_tokens: int) -> float:
        """Estimate cost based on token usage for DeepSeek models."""
        model_pricing = self.MODEL_PRICING.get(self.config.model)
        if not model_pricing:
            logger.warning(f"No pricing info for model {self.config.model}")
            return 0.0
        
        # DeepSeek pricing is per 1M tokens
        input_cost = (prompt_tokens / 1_000_000) * model_pricing["input"]
        output_cost = (completion_tokens / 1_000_000) * model_pricing["output"]
        
        return input_cost + output_cost
    
    def get_available_models(self) -> List[str]:
        """Get list of available DeepSeek models."""
        try:
            models = self.client.models.list()
            return [model.id for model in models.data]
        except Exception as e:
            logger.error(f"Failed to get available models: {e}")
            return list(self.MODEL_PRICING.keys())
    
    def validate_config(self) -> bool:
        """Validate DeepSeek configuration."""
        if not self.config.api_key:
            raise LLMError("API key is required for DeepSeek", provider=LLMProviderType.DEEPSEEK)
        
        if not self.config.model:
            raise LLMError("Model name is required for DeepSeek", provider=LLMProviderType.DEEPSEEK)
        
        # Test API key validity
        try:
            self.client.models.list()
            return True
        except Exception as e:
            raise LLMError(f"Invalid DeepSeek API key or configuration: {e}", provider=LLMProviderType.DEEPSEEK)
    
    def generate_embedding(self, text: str, **kwargs) -> List[float]:
        """
        Generate text embedding using DeepSeek API.
        Note: DeepSeek may not have dedicated embedding models.
        """
        # DeepSeek doesn't have dedicated embedding models yet
        # This is a placeholder implementation
        raise LLMError(
            "DeepSeek doesn't currently support embedding generation. Use OpenAI embeddings instead.",
            provider=LLMProviderType.DEEPSEEK
        )
    
    def health_check(self) -> bool:
        """Perform a health check on DeepSeek API."""
        try:
            # Test with a simple completion
            from .base import LLMMessage, MessageRole
            
            test_messages = [
                LLMMessage(role=MessageRole.USER, content="Hello, respond with 'OK'")
            ]
            response = self.chat_completion(test_messages)
            return bool(response.content)
        except Exception as e:
            logger.error(f"DeepSeek health check failed: {e}")
            return False
