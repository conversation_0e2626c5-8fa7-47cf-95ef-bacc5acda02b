"""
<PERSON><PERSON> (月之暗面) provider implementation.

This module implements the Kimi LLM provider with support for
Moonshot AI models via OpenAI-compatible API.
"""

from typing import List, Dict, Any, Optional
import logging

from .openai_provider import OpenAIProvider
from .base import LLMConfig, LLMError, LLMProviderType

logger = logging.getLogger(__name__)


class <PERSON><PERSON><PERSON><PERSON>ider(OpenAIProvider):
    """<PERSON><PERSON> (Moonshot AI) LLM provider implementation using OpenAI-compatible API."""
    
    # Kimi model pricing (per 1M tokens) - Updated as of 2025
    MODEL_PRICING = {
        "moonshot-v1-8k": {"input": 12.0, "output": 12.0},
        "moonshot-v1-32k": {"input": 24.0, "output": 24.0},
        "moonshot-v1-128k": {"input": 60.0, "output": 60.0},
        "moonshot-v1-auto": {"input": 12.0, "output": 12.0},  # Auto-scaling context
    }
    
    DEFAULT_BASE_URL = "https://api.moonshot.cn/v1"
    
    def __init__(self, config: LLMConfig):
        # Set default base URL for <PERSON><PERSON>
        if not config.base_url:
            config.base_url = self.DEFAULT_BASE_URL
        
        # Override provider type
        config.provider = LLMProviderType.KIMI
        
        super().__init__(config)
    
    def _initialize_client(self):
        """Initialize Kimi client using OpenAI-compatible interface."""
        try:
            from openai import OpenAI
            
            self.client = OpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url,
                timeout=self.config.timeout
            )
            logger.info(f"Kimi client initialized with model: {self.config.model}")
            
        except Exception as e:
            raise LLMError(f"Failed to initialize Kimi client: {e}", provider=LLMProviderType.KIMI)
    
    def supports_multimodal(self) -> bool:
        """Kimi supports multimodal input for certain models."""
        # Kimi v1 models support image input
        return self.config.model.startswith("moonshot-v1")
    
    def supports_function_calling(self) -> bool:
        """Check if current model supports function calling."""
        # Kimi models support function calling
        return True
    
    def estimate_cost(self, prompt_tokens: int, completion_tokens: int) -> float:
        """Estimate cost based on token usage for Kimi models."""
        model_pricing = self.MODEL_PRICING.get(self.config.model)
        if not model_pricing:
            logger.warning(f"No pricing info for model {self.config.model}")
            return 0.0
        
        # Kimi pricing is per 1M tokens, same rate for input and output
        total_cost = ((prompt_tokens + completion_tokens) / 1_000_000) * model_pricing["input"]
        
        return total_cost
    
    def get_available_models(self) -> List[str]:
        """Get list of available Kimi models."""
        try:
            models = self.client.models.list()
            return [model.id for model in models.data]
        except Exception as e:
            logger.error(f"Failed to get available models: {e}")
            return list(self.MODEL_PRICING.keys())
    
    def validate_config(self) -> bool:
        """Validate Kimi configuration."""
        if not self.config.api_key:
            raise LLMError("API key is required for Kimi", provider=LLMProviderType.KIMI)
        
        if not self.config.model:
            raise LLMError("Model name is required for Kimi", provider=LLMProviderType.KIMI)
        
        # Test API key validity
        try:
            self.client.models.list()
            return True
        except Exception as e:
            raise LLMError(f"Invalid Kimi API key or configuration: {e}", provider=LLMProviderType.KIMI)
    
    def generate_embedding(self, text: str, **kwargs) -> List[float]:
        """
        Generate text embedding using Kimi API.
        Note: Kimi may not have dedicated embedding models.
        """
        # Kimi doesn't have dedicated embedding models yet
        # This is a placeholder implementation
        raise LLMError(
            "Kimi doesn't currently support embedding generation. Use OpenAI embeddings instead.",
            provider=LLMProviderType.KIMI
        )
    
    def health_check(self) -> bool:
        """Perform a health check on Kimi API."""
        try:
            # Test with a simple completion
            from .base import LLMMessage, MessageRole
            
            test_messages = [
                LLMMessage(role=MessageRole.USER, content="你好，请回复'OK'")
            ]
            response = self.chat_completion(test_messages)
            return bool(response.content)
        except Exception as e:
            logger.error(f"Kimi health check failed: {e}")
            return False
    
    def get_context_length(self) -> int:
        """Get the context length for the current model."""
        context_lengths = {
            "moonshot-v1-8k": 8192,
            "moonshot-v1-32k": 32768,
            "moonshot-v1-128k": 131072,
            "moonshot-v1-auto": 131072,  # Max context for auto-scaling
        }
        return context_lengths.get(self.config.model, 8192)
