"""
OpenAI provider implementation.

This module implements the OpenAI LLM provider with support for GPT models
and embeddings.
"""

import base64
from typing import List, Dict, Any, Optional
import logging
from openai import OpenAI

from .base import (
    LLMProvider, EmbeddingProvider, LLMConfig, LLMMessage, LLMResponse, 
    LLMUsage, LLMError, LLMProviderType, MessageRole
)

logger = logging.getLogger(__name__)


class OpenAIProvider(LLMProvider, EmbeddingProvider):
    """OpenAI LLM provider implementation."""
    
    # Model pricing (per 1K tokens) - Updated as of 2025
    MODEL_PRICING = {
        "gpt-4": {"input": 0.03, "output": 0.06},
        "gpt-4-turbo": {"input": 0.01, "output": 0.03},
        "gpt-4o": {"input": 0.005, "output": 0.015},
        "gpt-4o-mini": {"input": 0.00015, "output": 0.0006},
        "gpt-3.5-turbo": {"input": 0.0005, "output": 0.0015},
        "text-embedding-3-small": {"input": 0.00002, "output": 0},
        "text-embedding-3-large": {"input": 0.00013, "output": 0},
    }
    
    MULTIMODAL_MODELS = {"gpt-4o", "gpt-4o-mini", "gpt-4-turbo"}
    
    def _initialize_client(self):
        """Initialize OpenAI client."""
        try:
            self.client = OpenAI(
                api_key=self.config.api_key,
                base_url=self.config.base_url,
                timeout=self.config.timeout
            )
            logger.info(f"OpenAI client initialized with model: {self.config.model}")
        except Exception as e:
            raise LLMError(f"Failed to initialize OpenAI client: {e}", provider=LLMProviderType.OPENAI)
    
    def chat_completion(self, messages: List[LLMMessage], **kwargs) -> LLMResponse:
        """Generate chat completion using OpenAI API."""
        try:
            # Convert messages to OpenAI format
            openai_messages = self._convert_messages(messages)
            
            # Prepare parameters
            params = {
                "model": self.config.model,
                "messages": openai_messages,
                "temperature": self.config.temperature,
                "max_tokens": self.config.max_tokens,
                "top_p": self.config.top_p,
                "frequency_penalty": self.config.frequency_penalty,
                "presence_penalty": self.config.presence_penalty,
                **self.config.extra_params,
                **kwargs
            }
            
            # Make API call
            response = self.client.chat.completions.create(**params)
            
            # Extract response data
            content = response.choices[0].message.content
            usage_data = response.usage
            
            # Create usage object
            usage = LLMUsage(
                prompt_tokens=usage_data.prompt_tokens,
                completion_tokens=usage_data.completion_tokens,
                total_tokens=usage_data.total_tokens,
                cost_usd=self.estimate_cost(usage_data.prompt_tokens, usage_data.completion_tokens)
            )
            
            return LLMResponse(
                content=content,
                model=response.model,
                usage=usage,
                metadata={
                    "finish_reason": response.choices[0].finish_reason,
                    "response_id": response.id
                }
            )
            
        except Exception as e:
            raise LLMError(f"OpenAI chat completion failed: {e}", provider=LLMProviderType.OPENAI)
    
    def generate_embedding(self, text: str, **kwargs) -> List[float]:
        """Generate text embedding using OpenAI API."""
        try:
            # Use embedding model if specified, otherwise use default
            embedding_model = kwargs.get('model', 'text-embedding-3-small')
            
            response = self.client.embeddings.create(
                model=embedding_model,
                input=text,
                **kwargs
            )
            
            return response.data[0].embedding
            
        except Exception as e:
            raise LLMError(f"OpenAI embedding generation failed: {e}", provider=LLMProviderType.OPENAI)
    
    def generate_batch_embeddings(self, texts: List[str], **kwargs) -> List[List[float]]:
        """Generate embeddings for multiple texts."""
        try:
            embedding_model = kwargs.get('model', 'text-embedding-3-small')
            
            response = self.client.embeddings.create(
                model=embedding_model,
                input=texts,
                **kwargs
            )
            
            return [item.embedding for item in response.data]
            
        except Exception as e:
            raise LLMError(f"OpenAI batch embedding generation failed: {e}", provider=LLMProviderType.OPENAI)
    
    def _convert_messages(self, messages: List[LLMMessage]) -> List[Dict[str, Any]]:
        """Convert internal message format to OpenAI format."""
        openai_messages = []
        
        for msg in messages:
            openai_msg = {
                "role": msg.role.value,
                "content": msg.content
            }
            
            # Handle multimodal content
            if msg.images and self.supports_multimodal():
                content_parts = [{"type": "text", "text": msg.content}]
                
                for image_data in msg.images:
                    content_parts.append({
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{image_data}"
                        }
                    })
                
                openai_msg["content"] = content_parts
            
            openai_messages.append(openai_msg)
        
        return openai_messages
    
    def supports_multimodal(self) -> bool:
        """Check if current model supports multimodal input."""
        return self.config.model in self.MULTIMODAL_MODELS
    
    def supports_function_calling(self) -> bool:
        """Check if current model supports function calling."""
        return self.config.model.startswith(("gpt-4", "gpt-3.5-turbo"))
    
    def estimate_cost(self, prompt_tokens: int, completion_tokens: int) -> float:
        """Estimate cost based on token usage."""
        model_pricing = self.MODEL_PRICING.get(self.config.model)
        if not model_pricing:
            logger.warning(f"No pricing info for model {self.config.model}")
            return 0.0
        
        input_cost = (prompt_tokens / 1000) * model_pricing["input"]
        output_cost = (completion_tokens / 1000) * model_pricing["output"]
        
        return input_cost + output_cost
    
    def get_available_models(self) -> List[str]:
        """Get list of available models."""
        try:
            models = self.client.models.list()
            return [model.id for model in models.data if model.id.startswith(("gpt-", "text-embedding"))]
        except Exception as e:
            logger.error(f"Failed to get available models: {e}")
            return []
    
    def validate_config(self) -> bool:
        """Validate OpenAI configuration."""
        super().validate_config()
        
        # Test API key validity
        try:
            self.client.models.list()
            return True
        except Exception as e:
            raise LLMError(f"Invalid OpenAI API key or configuration: {e}", provider=LLMProviderType.OPENAI)
