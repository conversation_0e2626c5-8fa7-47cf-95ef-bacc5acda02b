"""
LLM Manager for handling multiple providers with fallback and load balancing.

This module provides a high-level manager that can handle multiple LLM providers,
implement fallback strategies, load balancing, and cost optimization.
"""

import time
import random
from typing import List, Dict, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import logging

from .base import (
    LLMProvider, LLMConfig, LLMMessage, LLMResponse, 
    LLMError, LLMProviderType, LLMUsage
)
from .factory import LLMFactory

logger = logging.getLogger(__name__)


class FallbackStrategy(Enum):
    """Fallback strategies for provider failures."""
    NONE = "none"
    NEXT_AVAILABLE = "next_available"
    CHEAPEST_FIRST = "cheapest_first"
    FASTEST_FIRST = "fastest_first"


class LoadBalancingStrategy(Enum):
    """Load balancing strategies."""
    ROUND_ROBIN = "round_robin"
    RANDOM = "random"
    LEAST_COST = "least_cost"
    LEAST_LATENCY = "least_latency"
    WEIGHTED = "weighted"


@dataclass
class ProviderMetrics:
    """Metrics for a provider."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_cost: float = 0.0
    total_latency: float = 0.0
    last_used: Optional[float] = None
    health_status: bool = True
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests
    
    @property
    def average_latency(self) -> float:
        """Calculate average latency."""
        if self.successful_requests == 0:
            return 0.0
        return self.total_latency / self.successful_requests
    
    @property
    def average_cost(self) -> float:
        """Calculate average cost per request."""
        if self.successful_requests == 0:
            return 0.0
        return self.total_cost / self.successful_requests


@dataclass
class LLMManagerConfig:
    """Configuration for LLM Manager."""
    fallback_strategy: FallbackStrategy = FallbackStrategy.NEXT_AVAILABLE
    load_balancing_strategy: LoadBalancingStrategy = LoadBalancingStrategy.ROUND_ROBIN
    enable_health_checks: bool = True
    health_check_interval: int = 300  # seconds
    max_retries_per_provider: int = 2
    provider_weights: Dict[str, float] = field(default_factory=dict)
    cost_threshold: Optional[float] = None  # Max cost per request
    latency_threshold: Optional[float] = None  # Max latency in seconds


class LLMManager:
    """
    High-level manager for multiple LLM providers.
    
    Provides features like:
    - Multiple provider support
    - Automatic fallback
    - Load balancing
    - Cost optimization
    - Performance monitoring
    """
    
    def __init__(self, config: LLMManagerConfig = None):
        """Initialize LLM Manager."""
        self.config = config or LLMManagerConfig()
        self.providers: Dict[str, LLMProvider] = {}
        self.metrics: Dict[str, ProviderMetrics] = {}
        self.current_provider_index = 0
        self.last_health_check = 0
        
        logger.info("LLM Manager initialized")
    
    def add_provider(self, name: str, provider: LLMProvider, weight: float = 1.0):
        """
        Add a provider to the manager.
        
        Args:
            name: Unique name for the provider
            provider: LLM provider instance
            weight: Weight for load balancing (higher = more likely to be selected)
        """
        self.providers[name] = provider
        self.metrics[name] = ProviderMetrics()
        self.config.provider_weights[name] = weight
        
        logger.info(f"Added provider: {name} ({provider.provider_type.value})")
    
    def add_provider_from_config(self, name: str, config: LLMConfig, weight: float = 1.0):
        """Add a provider from configuration."""
        provider = LLMFactory.create_provider(config)
        self.add_provider(name, provider, weight)
    
    def remove_provider(self, name: str):
        """Remove a provider from the manager."""
        if name in self.providers:
            del self.providers[name]
            del self.metrics[name]
            if name in self.config.provider_weights:
                del self.config.provider_weights[name]
            logger.info(f"Removed provider: {name}")
    
    def chat_completion(self, messages: List[LLMMessage], 
                       preferred_provider: Optional[str] = None,
                       **kwargs) -> LLMResponse:
        """
        Generate chat completion with automatic provider selection and fallback.
        
        Args:
            messages: List of conversation messages
            preferred_provider: Preferred provider name (optional)
            **kwargs: Additional parameters
            
        Returns:
            LLMResponse from the selected provider
        """
        if not self.providers:
            raise LLMError("No providers available")
        
        # Perform health checks if needed
        self._check_provider_health()
        
        # Select provider
        if preferred_provider and preferred_provider in self.providers:
            provider_order = [preferred_provider]
        else:
            provider_order = self._select_providers()
        
        # Try providers in order
        last_error = None
        for provider_name in provider_order:
            provider = self.providers[provider_name]
            metrics = self.metrics[provider_name]
            
            # Skip unhealthy providers
            if not metrics.health_status:
                continue
            
            try:
                start_time = time.time()
                response = provider.chat_completion_with_retry(messages, **kwargs)
                latency = time.time() - start_time
                
                # Update metrics
                self._update_metrics_success(provider_name, response, latency)
                
                # Check thresholds
                if self._check_thresholds(response, latency):
                    return response
                else:
                    logger.warning(f"Provider {provider_name} exceeded thresholds")
                    continue
                    
            except Exception as e:
                last_error = e
                self._update_metrics_failure(provider_name, e)
                logger.warning(f"Provider {provider_name} failed: {e}")
                continue
        
        # All providers failed
        raise LLMError(f"All providers failed. Last error: {last_error}")
    
    def generate_embedding(self, text: str, 
                          preferred_provider: Optional[str] = None,
                          **kwargs) -> List[float]:
        """Generate text embedding with provider selection."""
        if not self.providers:
            raise LLMError("No providers available")
        
        # Select provider that supports embeddings
        embedding_providers = [
            name for name, provider in self.providers.items()
            if hasattr(provider, 'generate_embedding')
        ]
        
        if not embedding_providers:
            raise LLMError("No providers support embedding generation")
        
        if preferred_provider and preferred_provider in embedding_providers:
            provider_name = preferred_provider
        else:
            provider_name = embedding_providers[0]  # Use first available
        
        try:
            provider = self.providers[provider_name]
            return provider.generate_embedding(text, **kwargs)
        except Exception as e:
            raise LLMError(f"Embedding generation failed: {e}")
    
    def _select_providers(self) -> List[str]:
        """Select providers based on load balancing strategy."""
        healthy_providers = [
            name for name, metrics in self.metrics.items()
            if metrics.health_status
        ]
        
        if not healthy_providers:
            # Use all providers if none are healthy
            healthy_providers = list(self.providers.keys())
        
        strategy = self.config.load_balancing_strategy
        
        if strategy == LoadBalancingStrategy.ROUND_ROBIN:
            # Rotate through providers
            self.current_provider_index = (self.current_provider_index + 1) % len(healthy_providers)
            selected = healthy_providers[self.current_provider_index]
            return [selected] + [p for p in healthy_providers if p != selected]
        
        elif strategy == LoadBalancingStrategy.RANDOM:
            random.shuffle(healthy_providers)
            return healthy_providers
        
        elif strategy == LoadBalancingStrategy.LEAST_COST:
            # Sort by average cost
            return sorted(healthy_providers, key=lambda p: self.metrics[p].average_cost)
        
        elif strategy == LoadBalancingStrategy.LEAST_LATENCY:
            # Sort by average latency
            return sorted(healthy_providers, key=lambda p: self.metrics[p].average_latency)
        
        elif strategy == LoadBalancingStrategy.WEIGHTED:
            # Weighted random selection
            weights = [self.config.provider_weights.get(p, 1.0) for p in healthy_providers]
            selected = random.choices(healthy_providers, weights=weights)[0]
            return [selected] + [p for p in healthy_providers if p != selected]
        
        else:
            return healthy_providers
    
    def _check_provider_health(self):
        """Check provider health if needed."""
        current_time = time.time()
        if (self.config.enable_health_checks and 
            current_time - self.last_health_check > self.config.health_check_interval):
            
            for name, provider in self.providers.items():
                try:
                    is_healthy = provider.health_check()
                    self.metrics[name].health_status = is_healthy
                    logger.debug(f"Provider {name} health: {'OK' if is_healthy else 'FAILED'}")
                except Exception as e:
                    self.metrics[name].health_status = False
                    logger.warning(f"Health check failed for {name}: {e}")
            
            self.last_health_check = current_time
    
    def _update_metrics_success(self, provider_name: str, response: LLMResponse, latency: float):
        """Update metrics for successful request."""
        metrics = self.metrics[provider_name]
        metrics.total_requests += 1
        metrics.successful_requests += 1
        metrics.total_latency += latency
        metrics.last_used = time.time()
        
        if response.usage and response.usage.cost_usd:
            metrics.total_cost += response.usage.cost_usd
    
    def _update_metrics_failure(self, provider_name: str, error: Exception):
        """Update metrics for failed request."""
        metrics = self.metrics[provider_name]
        metrics.total_requests += 1
        metrics.failed_requests += 1
        
        # Mark as unhealthy if too many failures
        if metrics.success_rate < 0.5:
            metrics.health_status = False
    
    def _check_thresholds(self, response: LLMResponse, latency: float) -> bool:
        """Check if response meets configured thresholds."""
        if self.config.cost_threshold and response.usage and response.usage.cost_usd:
            if response.usage.cost_usd > self.config.cost_threshold:
                return False
        
        if self.config.latency_threshold and latency > self.config.latency_threshold:
            return False
        
        return True
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of all provider metrics."""
        summary = {}
        
        for name, metrics in self.metrics.items():
            summary[name] = {
                "total_requests": metrics.total_requests,
                "success_rate": metrics.success_rate,
                "average_latency": metrics.average_latency,
                "average_cost": metrics.average_cost,
                "total_cost": metrics.total_cost,
                "health_status": metrics.health_status,
                "provider_type": self.providers[name].provider_type.value
            }
        
        return summary
    
    def get_best_provider(self, criteria: str = "cost") -> Optional[str]:
        """
        Get the best provider based on criteria.
        
        Args:
            criteria: "cost", "latency", "success_rate"
            
        Returns:
            Name of the best provider
        """
        if not self.providers:
            return None
        
        healthy_providers = [
            name for name, metrics in self.metrics.items()
            if metrics.health_status and metrics.successful_requests > 0
        ]
        
        if not healthy_providers:
            return None
        
        if criteria == "cost":
            return min(healthy_providers, key=lambda p: self.metrics[p].average_cost)
        elif criteria == "latency":
            return min(healthy_providers, key=lambda p: self.metrics[p].average_latency)
        elif criteria == "success_rate":
            return max(healthy_providers, key=lambda p: self.metrics[p].success_rate)
        else:
            return healthy_providers[0]
