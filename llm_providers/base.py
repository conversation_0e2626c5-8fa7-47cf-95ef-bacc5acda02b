"""
Base classes for LLM provider abstraction.

This module defines the abstract base classes and interfaces for all LLM providers,
ensuring consistent behavior across different providers.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import time
import logging

logger = logging.getLogger(__name__)


class LLMProviderType(Enum):
    """Supported LLM provider types."""
    OPENAI = "openai"
    GEMINI = "gemini"
    DEEPSEEK = "deepseek"
    KIMI = "kimi"
    CLAUDE = "claude"
    OLLAMA = "ollama"


class MessageRole(Enum):
    """Message roles for chat completion."""
    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"


@dataclass
class LLMMessage:
    """Represents a message in the conversation."""
    role: MessageRole
    content: str
    images: Optional[List[str]] = None  # Base64 encoded images for multimodal
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LLMConfig:
    """Configuration for LLM providers."""
    provider: LLMProviderType
    model: str
    api_key: str
    base_url: Optional[str] = None
    temperature: float = 0.1
    max_tokens: int = 2000
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    timeout: int = 60
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # Provider-specific configurations
    extra_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class LLMUsage:
    """Token usage information."""
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    cost_usd: Optional[float] = None


@dataclass
class LLMResponse:
    """Response from LLM provider."""
    content: str
    model: str
    usage: Optional[LLMUsage] = None
    response_time: float = 0.0
    provider: Optional[LLMProviderType] = None
    metadata: Optional[Dict[str, Any]] = None


class LLMError(Exception):
    """Base exception for LLM-related errors."""
    
    def __init__(self, message: str, provider: Optional[LLMProviderType] = None, 
                 error_code: Optional[str] = None):
        super().__init__(message)
        self.provider = provider
        self.error_code = error_code


class LLMProvider(ABC):
    """Abstract base class for all LLM providers."""
    
    def __init__(self, config: LLMConfig):
        """Initialize the LLM provider with configuration."""
        self.config = config
        self.provider_type = config.provider
        self._client = None
        self._initialize_client()
    
    @abstractmethod
    def _initialize_client(self):
        """Initialize the provider-specific client."""
        pass
    
    @abstractmethod
    def chat_completion(self, messages: List[LLMMessage], **kwargs) -> LLMResponse:
        """
        Generate chat completion.
        
        Args:
            messages: List of conversation messages
            **kwargs: Additional provider-specific parameters
            
        Returns:
            LLMResponse with generated content
        """
        pass
    
    @abstractmethod
    def generate_embedding(self, text: str, **kwargs) -> List[float]:
        """
        Generate text embedding.
        
        Args:
            text: Input text to embed
            **kwargs: Additional provider-specific parameters
            
        Returns:
            List of embedding values
        """
        pass
    
    def chat_completion_with_retry(self, messages: List[LLMMessage], **kwargs) -> LLMResponse:
        """
        Generate chat completion with retry mechanism.
        
        Args:
            messages: List of conversation messages
            **kwargs: Additional parameters
            
        Returns:
            LLMResponse with generated content
            
        Raises:
            LLMError: If all retry attempts fail
        """
        last_error = None
        
        for attempt in range(self.config.max_retries):
            try:
                start_time = time.time()
                response = self.chat_completion(messages, **kwargs)
                response.response_time = time.time() - start_time
                response.provider = self.provider_type
                return response
                
            except Exception as e:
                last_error = e
                logger.warning(f"LLM call attempt {attempt + 1} failed: {e}")
                
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay * (2 ** attempt))  # Exponential backoff
                    
        raise LLMError(
            f"All {self.config.max_retries} retry attempts failed. Last error: {last_error}",
            provider=self.provider_type
        )
    
    def supports_multimodal(self) -> bool:
        """Check if the provider supports multimodal input."""
        return False
    
    def supports_function_calling(self) -> bool:
        """Check if the provider supports function calling."""
        return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model."""
        return {
            "provider": self.provider_type.value,
            "model": self.config.model,
            "supports_multimodal": self.supports_multimodal(),
            "supports_function_calling": self.supports_function_calling()
        }
    
    def estimate_cost(self, prompt_tokens: int, completion_tokens: int) -> float:
        """
        Estimate the cost for a given number of tokens.
        
        Args:
            prompt_tokens: Number of input tokens
            completion_tokens: Number of output tokens
            
        Returns:
            Estimated cost in USD
        """
        # Default implementation - should be overridden by providers
        return 0.0
    
    def validate_config(self) -> bool:
        """Validate the provider configuration."""
        if not self.config.api_key:
            raise LLMError(f"API key is required for {self.provider_type.value}")
        
        if not self.config.model:
            raise LLMError(f"Model name is required for {self.provider_type.value}")
            
        return True
    
    def health_check(self) -> bool:
        """Perform a health check on the provider."""
        try:
            # Simple test message
            test_messages = [
                LLMMessage(role=MessageRole.USER, content="Hello")
            ]
            response = self.chat_completion(test_messages)
            return bool(response.content)
        except Exception as e:
            logger.error(f"Health check failed for {self.provider_type.value}: {e}")
            return False


class EmbeddingProvider(ABC):
    """Abstract base class for embedding providers."""
    
    def __init__(self, config: LLMConfig):
        self.config = config
        self.provider_type = config.provider
    
    @abstractmethod
    def generate_embedding(self, text: str, **kwargs) -> List[float]:
        """Generate text embedding."""
        pass
    
    @abstractmethod
    def generate_batch_embeddings(self, texts: List[str], **kwargs) -> List[List[float]]:
        """Generate embeddings for multiple texts."""
        pass
