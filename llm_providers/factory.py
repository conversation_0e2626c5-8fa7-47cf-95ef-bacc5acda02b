"""
LLM Provider Factory for creating and managing different LLM providers.

This module provides factory functions and utilities for creating
LLM provider instances based on configuration.
"""

from typing import Dict, Type, Optional, List
import logging
import os

from .base import <PERSON><PERSON>rovider, LLMConfig, LLMProviderType, LLMError
from .openai_provider import OpenAIProvider
from .gemini_provider import GeminiProvider
from .deepseek_provider import DeepSeekProvider
from .kimi_provider import Kimi<PERSON><PERSON>ider

logger = logging.getLogger(__name__)


class LLMFactory:
    """Factory class for creating LLM providers."""
    
    # Registry of available providers
    _providers: Dict[LLMProviderType, Type[LLMProvider]] = {
        LLMProviderType.OPENAI: OpenAIProvider,
        LLMProviderType.GEMINI: GeminiProvider,
        LLMProviderType.DEEPSEEK: DeepSeekProvider,
        LLMProviderType.KIMI: KimiProvider,
    }
    
    # Default models for each provider
    _default_models = {
        LLMProviderType.OPENAI: "gpt-4o-mini",
        LLMProviderType.GEMINI: "gemini-1.5-flash",
        LLMProviderType.DEEPSEEK: "deepseek-chat",
        LLMProviderType.KIMI: "moonshot-v1-8k",
    }
    
    @classmethod
    def create_provider(cls, config: LLMConfig) -> LLMProvider:
        """
        Create an LLM provider instance based on configuration.
        
        Args:
            config: LLM configuration
            
        Returns:
            LLM provider instance
            
        Raises:
            LLMError: If provider type is not supported
        """
        provider_class = cls._providers.get(config.provider)
        if not provider_class:
            raise LLMError(f"Unsupported provider type: {config.provider}")
        
        try:
            return provider_class(config)
        except Exception as e:
            raise LLMError(f"Failed to create {config.provider.value} provider: {e}")
    
    @classmethod
    def create_from_env(cls, provider_type: LLMProviderType, 
                       model: Optional[str] = None,
                       **kwargs) -> LLMProvider:
        """
        Create an LLM provider from environment variables.
        
        Args:
            provider_type: Type of provider to create
            model: Model name (optional, uses default if not provided)
            **kwargs: Additional configuration parameters
            
        Returns:
            LLM provider instance
        """
        # Get API key from environment
        api_key_env_vars = {
            LLMProviderType.OPENAI: "OPENAI_API_KEY",
            LLMProviderType.GEMINI: "GEMINI_API_KEY",
            LLMProviderType.DEEPSEEK: "DEEPSEEK_API_KEY",
            LLMProviderType.KIMI: "KIMI_API_KEY",
        }
        
        api_key_var = api_key_env_vars.get(provider_type)
        if not api_key_var:
            raise LLMError(f"No API key environment variable defined for {provider_type.value}")
        
        api_key = os.getenv(api_key_var)
        if not api_key:
            raise LLMError(f"Environment variable {api_key_var} not set")
        
        # Use default model if not provided
        if not model:
            model = cls._default_models.get(provider_type)
        
        # Create configuration
        config = LLMConfig(
            provider=provider_type,
            model=model,
            api_key=api_key,
            **kwargs
        )
        
        return cls.create_provider(config)
    
    @classmethod
    def get_supported_providers(cls) -> List[LLMProviderType]:
        """Get list of supported provider types."""
        return list(cls._providers.keys())
    
    @classmethod
    def register_provider(cls, provider_type: LLMProviderType, 
                         provider_class: Type[LLMProvider]):
        """
        Register a new provider type.
        
        Args:
            provider_type: Provider type enum
            provider_class: Provider implementation class
        """
        cls._providers[provider_type] = provider_class
        logger.info(f"Registered provider: {provider_type.value}")
    
    @classmethod
    def get_default_model(cls, provider_type: LLMProviderType) -> Optional[str]:
        """Get default model for a provider type."""
        return cls._default_models.get(provider_type)
    
    @classmethod
    def set_default_model(cls, provider_type: LLMProviderType, model: str):
        """Set default model for a provider type."""
        cls._default_models[provider_type] = model


def create_llm_provider(provider: str, model: str = None, 
                       api_key: str = None, **kwargs) -> LLMProvider:
    """
    Convenience function to create an LLM provider.
    
    Args:
        provider: Provider name (e.g., "openai", "gemini", "deepseek", "kimi")
        model: Model name (optional)
        api_key: API key (optional, will try to get from environment)
        **kwargs: Additional configuration parameters
        
    Returns:
        LLM provider instance
    """
    # Convert string to enum
    try:
        provider_type = LLMProviderType(provider.lower())
    except ValueError:
        raise LLMError(f"Unsupported provider: {provider}")
    
    # Get API key from environment if not provided
    if not api_key:
        api_key_env_vars = {
            LLMProviderType.OPENAI: "OPENAI_API_KEY",
            LLMProviderType.GEMINI: "GEMINI_API_KEY", 
            LLMProviderType.DEEPSEEK: "DEEPSEEK_API_KEY",
            LLMProviderType.KIMI: "KIMI_API_KEY",
        }
        
        api_key_var = api_key_env_vars.get(provider_type)
        if api_key_var:
            api_key = os.getenv(api_key_var)
    
    if not api_key:
        raise LLMError(f"API key required for {provider}")
    
    # Use default model if not provided
    if not model:
        model = LLMFactory.get_default_model(provider_type)
    
    # Create configuration
    config = LLMConfig(
        provider=provider_type,
        model=model,
        api_key=api_key,
        **kwargs
    )
    
    return LLMFactory.create_provider(config)


def create_multi_provider_config() -> Dict[str, LLMConfig]:
    """
    Create configurations for all available providers based on environment variables.
    
    Returns:
        Dictionary mapping provider names to configurations
    """
    configs = {}
    
    for provider_type in LLMFactory.get_supported_providers():
        try:
            provider = LLMFactory.create_from_env(provider_type)
            configs[provider_type.value] = provider.config
        except LLMError as e:
            logger.warning(f"Could not create config for {provider_type.value}: {e}")
    
    return configs
