"""
Google Gemini provider implementation.

This module implements the Google Gemini LLM provider with support for
Gemini Pro and other Google AI models.
"""

import base64
from typing import List, Dict, Any, Optional
import logging

try:
    import google.generativeai as genai
    from google.generativeai.types import HarmCategory, HarmBlockThreshold
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    genai = None

from .base import (
    LLMProvider, EmbeddingProvider, LLMConfig, LLMMessage, LLMResponse, 
    LLMUsage, LLMError, LLMProviderType, MessageRole
)

logger = logging.getLogger(__name__)


class GeminiProvider(LLMProvider):
    """Google Gemini LLM provider implementation."""
    
    # Model pricing (per 1M tokens) - Updated as of 2025
    MODEL_PRICING = {
        "gemini-1.5-pro": {"input": 3.5, "output": 10.5},
        "gemini-1.5-flash": {"input": 0.075, "output": 0.3},
        "gemini-1.0-pro": {"input": 0.5, "output": 1.5},
        "gemini-pro-vision": {"input": 0.25, "output": 0.5},
    }
    
    MULTIMODAL_MODELS = {"gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro-vision"}
    
    def __init__(self, config: LLMConfig):
        if not GEMINI_AVAILABLE:
            raise LLMError(
                "Google Generative AI library not installed. Install with: pip install google-generativeai",
                provider=LLMProviderType.GEMINI
            )
        super().__init__(config)
    
    def _initialize_client(self):
        """Initialize Gemini client."""
        try:
            genai.configure(api_key=self.config.api_key)
            
            # Configure safety settings
            safety_settings = {
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }
            
            # Initialize model
            generation_config = genai.types.GenerationConfig(
                temperature=self.config.temperature,
                max_output_tokens=self.config.max_tokens,
                top_p=self.config.top_p,
            )
            
            self.model = genai.GenerativeModel(
                model_name=self.config.model,
                generation_config=generation_config,
                safety_settings=safety_settings
            )
            
            logger.info(f"Gemini client initialized with model: {self.config.model}")
            
        except Exception as e:
            raise LLMError(f"Failed to initialize Gemini client: {e}", provider=LLMProviderType.GEMINI)
    
    def chat_completion(self, messages: List[LLMMessage], **kwargs) -> LLMResponse:
        """Generate chat completion using Gemini API."""
        try:
            # Convert messages to Gemini format
            gemini_messages = self._convert_messages(messages)
            
            # Start chat session
            chat = self.model.start_chat(history=gemini_messages[:-1])
            
            # Get the last message (user input)
            last_message = gemini_messages[-1]
            
            # Generate response
            response = chat.send_message(last_message)
            
            # Extract usage information if available
            usage = LLMUsage()
            if hasattr(response, 'usage_metadata'):
                usage.prompt_tokens = getattr(response.usage_metadata, 'prompt_token_count', 0)
                usage.completion_tokens = getattr(response.usage_metadata, 'candidates_token_count', 0)
                usage.total_tokens = usage.prompt_tokens + usage.completion_tokens
                usage.cost_usd = self.estimate_cost(usage.prompt_tokens, usage.completion_tokens)
            
            return LLMResponse(
                content=response.text,
                model=self.config.model,
                usage=usage,
                metadata={
                    "finish_reason": getattr(response.candidates[0], 'finish_reason', None),
                    "safety_ratings": getattr(response.candidates[0], 'safety_ratings', [])
                }
            )
            
        except Exception as e:
            raise LLMError(f"Gemini chat completion failed: {e}", provider=LLMProviderType.GEMINI)
    
    def generate_embedding(self, text: str, **kwargs) -> List[float]:
        """Generate text embedding using Gemini API."""
        try:
            # Gemini uses a different embedding model
            embedding_model = kwargs.get('model', 'models/embedding-001')
            
            result = genai.embed_content(
                model=embedding_model,
                content=text,
                task_type="retrieval_document"
            )
            
            return result['embedding']
            
        except Exception as e:
            raise LLMError(f"Gemini embedding generation failed: {e}", provider=LLMProviderType.GEMINI)
    
    def _convert_messages(self, messages: List[LLMMessage]) -> List[Dict[str, Any]]:
        """Convert internal message format to Gemini format."""
        gemini_messages = []
        
        for msg in messages:
            # Gemini uses 'user' and 'model' roles
            role = "user" if msg.role == MessageRole.USER else "model"
            
            if msg.role == MessageRole.SYSTEM:
                # System messages are handled differently in Gemini
                # We'll prepend them to the first user message
                continue
            
            content_parts = [msg.content]
            
            # Handle multimodal content
            if msg.images and self.supports_multimodal():
                for image_data in msg.images:
                    # Decode base64 image
                    image_bytes = base64.b64decode(image_data)
                    content_parts.append({
                        "mime_type": "image/jpeg",
                        "data": image_bytes
                    })
            
            gemini_messages.append({
                "role": role,
                "parts": content_parts
            })
        
        return gemini_messages
    
    def supports_multimodal(self) -> bool:
        """Check if current model supports multimodal input."""
        return self.config.model in self.MULTIMODAL_MODELS
    
    def supports_function_calling(self) -> bool:
        """Check if current model supports function calling."""
        return self.config.model in {"gemini-1.5-pro", "gemini-1.5-flash"}
    
    def estimate_cost(self, prompt_tokens: int, completion_tokens: int) -> float:
        """Estimate cost based on token usage."""
        model_pricing = self.MODEL_PRICING.get(self.config.model)
        if not model_pricing:
            logger.warning(f"No pricing info for model {self.config.model}")
            return 0.0
        
        # Gemini pricing is per 1M tokens
        input_cost = (prompt_tokens / 1_000_000) * model_pricing["input"]
        output_cost = (completion_tokens / 1_000_000) * model_pricing["output"]
        
        return input_cost + output_cost
    
    def get_available_models(self) -> List[str]:
        """Get list of available Gemini models."""
        try:
            models = genai.list_models()
            return [model.name for model in models if 'generateContent' in model.supported_generation_methods]
        except Exception as e:
            logger.error(f"Failed to get available models: {e}")
            return []
    
    def validate_config(self) -> bool:
        """Validate Gemini configuration."""
        super().validate_config()
        
        # Test API key validity
        try:
            list(genai.list_models())
            return True
        except Exception as e:
            raise LLMError(f"Invalid Gemini API key or configuration: {e}", provider=LLMProviderType.GEMINI)
