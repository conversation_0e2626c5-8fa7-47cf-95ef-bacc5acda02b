#!/usr/bin/env python3
"""
Performance Monitoring Test Suite for Task 5.2

This module contains comprehensive performance monitoring and optimization tests
for the Blender AI Agent system.

Author: Augment Agent
Date: 2025-07-19
"""

import unittest
import time
import psutil
import threading
import json
import tempfile
import shutil
import os
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock
from typing import Dict, List, Any
from dataclasses import dataclass, asdict
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Import system components
from agents.knowledge_agent import KnowledgeAgent
from agents.image_analysis_agent import ImageAnalysisAgent
from agents.spec_generation_agent import SpecGenerationAgent
from agents.code_generation_agent import CodeGenerationAgent
from input_module.image_handler import ImageHandler


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""
    component: str
    operation: str
    execution_time: float
    memory_usage_mb: float
    cpu_usage_percent: float
    timestamp: str
    success: bool
    error_message: str = ""


class PerformanceMonitor:
    """Performance monitoring utility."""
    
    def __init__(self):
        self.metrics: List[PerformanceMetrics] = []
        self.process = psutil.Process()
    
    def start_monitoring(self, component: str, operation: str):
        """Start monitoring a component operation."""
        return PerformanceContext(self, component, operation)
    
    def record_metric(self, metric: PerformanceMetrics):
        """Record a performance metric."""
        self.metrics.append(metric)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of performance metrics."""
        if not self.metrics:
            return {}
        
        total_time = sum(m.execution_time for m in self.metrics)
        avg_memory = sum(m.memory_usage_mb for m in self.metrics) / len(self.metrics)
        avg_cpu = sum(m.cpu_usage_percent for m in self.metrics) / len(self.metrics)
        success_rate = sum(1 for m in self.metrics if m.success) / len(self.metrics) * 100
        
        by_component = {}
        for metric in self.metrics:
            if metric.component not in by_component:
                by_component[metric.component] = []
            by_component[metric.component].append(metric.execution_time)
        
        component_averages = {
            comp: sum(times) / len(times) 
            for comp, times in by_component.items()
        }
        
        return {
            'total_operations': len(self.metrics),
            'total_execution_time': total_time,
            'average_memory_usage_mb': avg_memory,
            'average_cpu_usage_percent': avg_cpu,
            'success_rate_percent': success_rate,
            'component_averages': component_averages,
            'slowest_operations': sorted(
                self.metrics, 
                key=lambda m: m.execution_time, 
                reverse=True
            )[:5]
        }
    
    def save_report(self, filepath: str):
        """Save performance report to file."""
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': self.get_metrics_summary(),
            'detailed_metrics': [asdict(m) for m in self.metrics]
        }
        
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2, default=str)


class PerformanceContext:
    """Context manager for performance monitoring."""
    
    def __init__(self, monitor: PerformanceMonitor, component: str, operation: str):
        self.monitor = monitor
        self.component = component
        self.operation = operation
        self.start_time = None
        self.start_memory = None
        self.start_cpu = None
        self.success = True
        self.error_message = ""
    
    def __enter__(self):
        self.start_time = time.time()
        self.start_memory = self.monitor.process.memory_info().rss / 1024 / 1024
        self.start_cpu = self.monitor.process.cpu_percent()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        execution_time = time.time() - self.start_time
        end_memory = self.monitor.process.memory_info().rss / 1024 / 1024
        end_cpu = self.monitor.process.cpu_percent()
        
        if exc_type is not None:
            self.success = False
            self.error_message = str(exc_val)
        
        metric = PerformanceMetrics(
            component=self.component,
            operation=self.operation,
            execution_time=execution_time,
            memory_usage_mb=end_memory,
            cpu_usage_percent=end_cpu,
            timestamp=datetime.now().isoformat(),
            success=self.success,
            error_message=self.error_message
        )
        
        self.monitor.record_metric(metric)


class TestPerformanceMonitoring(unittest.TestCase):
    """Test performance monitoring capabilities."""
    
    def setUp(self):
        """Set up performance monitoring tests."""
        self.temp_dir = tempfile.mkdtemp()
        self.monitor = PerformanceMonitor()
        
        # Initialize components
        self.image_handler = ImageHandler()
        self.image_analysis_agent = ImageAnalysisAgent()
        self.knowledge_agent = KnowledgeAgent()
        self.spec_generation_agent = SpecGenerationAgent()
        self.code_generation_agent = CodeGenerationAgent()
    
    def tearDown(self):
        """Clean up performance monitoring tests."""
        shutil.rmtree(self.temp_dir)
    
    def test_component_performance_profiling(self):
        """Test performance profiling of individual components."""
        print("\n📊 Testing Component Performance Profiling")
        
        # Test Image Handler performance
        with self.monitor.start_monitoring("ImageHandler", "process_local_image"):
            with patch.object(self.image_handler, 'process_local_image') as mock_process:
                mock_process.return_value = {
                    'processed_path': 'test.png',
                    'processing_time': 0.5
                }
                self.image_handler.process_local_image('test.png')
        
        # Test Knowledge Agent performance
        with self.monitor.start_monitoring("KnowledgeAgent", "query_knowledge"):
            with patch.object(self.knowledge_agent, 'query_knowledge') as mock_query:
                mock_query.return_value = [{'chunk': {'content': 'test'}, 'score': 0.8}]
                self.knowledge_agent.query_knowledge('test query')
        
        # Test Spec Generation performance
        with self.monitor.start_monitoring("SpecGenerationAgent", "generate_specification"):
            with patch.object(self.spec_generation_agent, 'generate_specification') as mock_spec:
                mock_spec.return_value = {
                    'specification': {'schema_version': 'v2.0.0'},
                    'generation_time': 2.0
                }
                self.spec_generation_agent.generate_specification(None, {})
        
        # Test Code Generation performance
        with self.monitor.start_monitoring("CodeGenerationAgent", "generate_code"):
            with patch.object(self.code_generation_agent, 'generate_code') as mock_code:
                mock_code.return_value = {
                    'code': 'import bpy',
                    'generation_time': 3.0
                }
                self.code_generation_agent.generate_code({})
        
        # Analyze results
        summary = self.monitor.get_metrics_summary()
        
        print(f"   Total operations: {summary['total_operations']}")
        print(f"   Success rate: {summary['success_rate_percent']:.1f}%")
        print(f"   Average memory usage: {summary['average_memory_usage_mb']:.1f} MB")
        
        # Verify performance requirements
        self.assertEqual(summary['total_operations'], 4)
        self.assertEqual(summary['success_rate_percent'], 100.0)
        
        # Check component-specific performance
        for component, avg_time in summary['component_averages'].items():
            print(f"   {component}: {avg_time:.3f}s average")
            self.assertLess(avg_time, 10.0, f"{component} should complete within 10s")
        
        return summary
    
    def test_memory_leak_detection(self):
        """Test memory leak detection during repeated operations."""
        print("\n🧠 Testing Memory Leak Detection")
        
        initial_memory = self.monitor.process.memory_info().rss / 1024 / 1024
        memory_samples = [initial_memory]
        
        # Perform repeated operations
        for i in range(20):
            with self.monitor.start_monitoring("MemoryTest", f"iteration_{i}"):
                # Simulate memory-intensive operation
                with patch.object(self.knowledge_agent, 'query_knowledge') as mock_query:
                    mock_query.return_value = [{'chunk': {'content': f'test_{i}'}, 'score': 0.8}]
                    self.knowledge_agent.query_knowledge(f'test query {i}')
                
                # Sample memory usage
                current_memory = self.monitor.process.memory_info().rss / 1024 / 1024
                memory_samples.append(current_memory)
        
        final_memory = memory_samples[-1]
        memory_growth = final_memory - initial_memory
        
        # Calculate memory growth trend
        if len(memory_samples) > 10:
            recent_avg = sum(memory_samples[-5:]) / 5
            early_avg = sum(memory_samples[:5]) / 5
            growth_trend = recent_avg - early_avg
        else:
            growth_trend = memory_growth
        
        print(f"   Initial memory: {initial_memory:.1f} MB")
        print(f"   Final memory: {final_memory:.1f} MB")
        print(f"   Memory growth: {memory_growth:.1f} MB")
        print(f"   Growth trend: {growth_trend:.1f} MB")
        
        # Verify no significant memory leaks
        self.assertLess(memory_growth, 100.0, "Memory growth should be < 100MB")
        self.assertLess(growth_trend, 50.0, "Memory growth trend should be < 50MB")
        
        return {
            'initial_memory': initial_memory,
            'final_memory': final_memory,
            'memory_growth': memory_growth,
            'growth_trend': growth_trend
        }
    
    def test_concurrent_performance(self):
        """Test performance under concurrent load."""
        print("\n🔄 Testing Concurrent Performance")
        
        import threading
        import queue
        
        results_queue = queue.Queue()
        num_threads = 5
        operations_per_thread = 4
        
        def worker_thread(thread_id):
            """Worker thread for concurrent testing."""
            thread_results = []
            
            for i in range(operations_per_thread):
                operation_name = f"thread_{thread_id}_op_{i}"
                
                with self.monitor.start_monitoring("ConcurrentTest", operation_name):
                    # Simulate work
                    time.sleep(0.1)
                    
                    # Mock component operation
                    with patch.object(self.image_analysis_agent, 'analyze_image') as mock_analyze:
                        from agents.image_analysis_agent import ImageAnalysisResult
                        mock_analyze.return_value = ImageAnalysisResult(
                            detected_shapes=[],
                            scene_description=f"Test scene {thread_id}_{i}",
                            confidence_score=0.9,
                            processing_time=0.1
                        )
                        result = self.image_analysis_agent.analyze_image(f"test_{thread_id}_{i}.png")
                        thread_results.append(result)
            
            results_queue.put(thread_results)
        
        # Start concurrent threads
        threads = []
        start_time = time.time()
        
        for thread_id in range(num_threads):
            thread = threading.Thread(target=worker_thread, args=(thread_id,))
            threads.append(thread)
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # Collect results
        all_results = []
        while not results_queue.empty():
            thread_results = results_queue.get()
            all_results.extend(thread_results)
        
        expected_operations = num_threads * operations_per_thread
        actual_operations = len(all_results)
        
        print(f"   Threads: {num_threads}")
        print(f"   Operations per thread: {operations_per_thread}")
        print(f"   Total time: {total_time:.2f}s")
        print(f"   Operations completed: {actual_operations}/{expected_operations}")
        print(f"   Average time per operation: {total_time/actual_operations:.3f}s")
        
        # Verify concurrent performance
        self.assertEqual(actual_operations, expected_operations)
        self.assertLess(total_time, 10.0, "Concurrent operations should complete within 10s")
        
        return {
            'total_time': total_time,
            'operations_completed': actual_operations,
            'avg_time_per_operation': total_time / actual_operations
        }
    
    def test_performance_report_generation(self):
        """Test performance report generation."""
        print("\n📋 Testing Performance Report Generation")
        
        # Generate some test metrics
        self.test_component_performance_profiling()
        
        # Generate report
        report_path = Path(self.temp_dir) / "performance_report.json"
        self.monitor.save_report(str(report_path))
        
        # Verify report was created
        self.assertTrue(report_path.exists())
        
        # Verify report content
        with open(report_path, 'r') as f:
            report = json.load(f)
        
        self.assertIn('timestamp', report)
        self.assertIn('summary', report)
        self.assertIn('detailed_metrics', report)
        
        summary = report['summary']
        self.assertIn('total_operations', summary)
        self.assertIn('success_rate_percent', summary)
        self.assertIn('component_averages', summary)
        
        print(f"   Report saved to: {report_path}")
        print(f"   Report size: {report_path.stat().st_size} bytes")
        print(f"   Operations recorded: {summary['total_operations']}")
        
        return str(report_path)


if __name__ == '__main__':
    unittest.main()
