# 多LLM提供商支持 - 迁移指南

## 概述

本指南说明如何将现有的Blender AI Agent系统升级为支持多种LLM提供商，包括OpenAI、Google Gemini、DeepSeek、Kimi等最新模型。

## 新增功能

### 🆕 支持的LLM提供商

| 提供商 | 模型示例 | 特点 | 成本 |
|--------|----------|------|------|
| **OpenAI** | gpt-4o, gpt-4o-mini, gpt-4 | 高质量、多模态支持 | 中等-高 |
| **Google Gemini** | gemini-1.5-pro, gemini-1.5-flash | 长上下文、多模态 | 低-中等 |
| **DeepSeek** | deepseek-chat, deepseek-coder | 代码生成优化 | 低 |
| **Kimi (月之暗面)** | moonshot-v1-8k, moonshot-v1-32k | 长上下文、中文优化 | 中等 |

### 🔧 核心特性

- **统一接口**: 所有提供商使用相同的API接口
- **自动回退**: 主要提供商失败时自动切换到备用提供商
- **负载均衡**: 支持多种负载均衡策略
- **成本优化**: 自动选择最经济的提供商
- **性能监控**: 实时监控各提供商的性能和成本
- **健康检查**: 自动检测提供商可用性

## 安装和配置

### 1. 安装依赖

```bash
# 基础依赖（已包含在requirements.txt中）
pip install openai

# 可选依赖
pip install google-generativeai  # For Gemini support
```

### 2. 环境变量配置

更新 `.env` 文件：

```bash
# 主要LLM提供商
PRIMARY_LLM_PROVIDER=openai

# API密钥配置
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
KIMI_API_KEY=your_kimi_api_key_here

# 模型配置
OPENAI_MODEL=gpt-4o-mini
GEMINI_MODEL=gemini-1.5-flash
DEEPSEEK_MODEL=deepseek-chat
KIMI_MODEL=moonshot-v1-8k

# 管理器配置
ENABLE_LLM_FALLBACK=true
LLM_LOAD_BALANCING=least_cost
LLM_COST_THRESHOLD=0.10
```

## 迁移步骤

### 步骤1: 更新Agent基类

创建新的基类 `agents/base_agent.py`：

```python
from llm_providers import LLMManager, LLMManagerConfig, create_llm_provider
from llm_providers.base import LLMMessage, MessageRole
import os

class BaseLLMAgent:
    """Base class for agents using LLM providers."""
    
    def __init__(self, preferred_provider: str = None):
        self.preferred_provider = preferred_provider or os.getenv('PRIMARY_LLM_PROVIDER', 'openai')
        self._setup_llm_manager()
    
    def _setup_llm_manager(self):
        """Setup LLM manager with multiple providers."""
        config = LLMManagerConfig(
            enable_health_checks=True,
            cost_threshold=float(os.getenv('LLM_COST_THRESHOLD', '0.10'))
        )
        
        self.llm_manager = LLMManager(config)
        
        # Add available providers
        providers = ['openai', 'gemini', 'deepseek', 'kimi']
        for provider in providers:
            try:
                llm_provider = create_llm_provider(provider)
                self.llm_manager.add_provider(provider, llm_provider)
            except Exception as e:
                print(f"Warning: Could not add {provider}: {e}")
    
    def call_llm(self, messages, **kwargs):
        """Call LLM with automatic provider selection."""
        return self.llm_manager.chat_completion(
            messages, 
            preferred_provider=self.preferred_provider,
            **kwargs
        )
```

### 步骤2: 更新SpecGenerationAgent

修改 `agents/spec_generation_agent.py`：

```python
# 原有代码
class SpecGenerationAgent:
    def __init__(self, config=None, knowledge_agent=None, openai_api_key=None):
        # 旧的OpenAI客户端初始化
        self.openai_client = OpenAI(api_key=openai_api_key)
    
    def _call_llm_with_retry(self, prompt: str) -> str:
        # 旧的OpenAI调用
        response = self.openai_client.chat.completions.create(...)

# 新代码
from .base_agent import BaseLLMAgent
from llm_providers.base import LLMMessage, MessageRole

class SpecGenerationAgent(BaseLLMAgent):
    def __init__(self, config=None, knowledge_agent=None, preferred_provider=None):
        super().__init__(preferred_provider)
        self.config = config or SpecGenerationConfig()
        self.knowledge_agent = knowledge_agent
    
    def _call_llm_with_retry(self, prompt: str) -> str:
        messages = [
            LLMMessage(role=MessageRole.SYSTEM, content="You are a 3D modeling expert."),
            LLMMessage(role=MessageRole.USER, content=prompt)
        ]
        response = self.call_llm(messages)
        return response.content
```

### 步骤3: 更新CodeGenerationAgent

类似地更新 `agents/code_generation_agent.py`：

```python
class CodeGenerationAgent(BaseLLMAgent):
    def __init__(self, config=None, knowledge_agent=None, preferred_provider=None):
        super().__init__(preferred_provider)
        self.config = config or CodeGenerationConfig()
        self.knowledge_agent = knowledge_agent
    
    def generate_code_with_llm(self, specification, knowledge_context):
        messages = [
            LLMMessage(role=MessageRole.SYSTEM, content="You are an expert Blender Python developer."),
            LLMMessage(role=MessageRole.USER, content=self._create_code_generation_prompt(specification))
        ]
        response = self.call_llm(messages)
        return response.content
```

### 步骤4: 更新VisualCriticAgent

更新 `agents/visual_critic_agent.py` 以支持多模态：

```python
class VisualCriticAgent(BaseLLMAgent):
    def __init__(self, config=None):
        # 优先使用支持多模态的提供商
        super().__init__(preferred_provider='openai')  # 或 'gemini'
        self.config = config or VisualCriticConfig()
    
    def _call_multimodal_llm_with_retry(self, messages):
        # 确保使用支持多模态的提供商
        multimodal_providers = ['openai', 'gemini']
        for provider in multimodal_providers:
            try:
                response = self.llm_manager.chat_completion(
                    messages, 
                    preferred_provider=provider
                )
                return response.content
            except Exception as e:
                continue
        raise Exception("No multimodal provider available")
```

### 步骤5: 更新主编排器

修改 `main_orchestrator.py`：

```python
class OrchestratorAgent:
    def _initialize_agents(self):
        # 使用环境变量配置首选提供商
        preferred_provider = os.getenv('PRIMARY_LLM_PROVIDER', 'openai')
        
        # 初始化各个Agent时传入首选提供商
        self.spec_generation_agent = SpecGenerationAgent(
            config=spec_config,
            knowledge_agent=self.knowledge_agent,
            preferred_provider=preferred_provider
        )
        
        self.code_generation_agent = CodeGenerationAgent(
            config=code_config,
            knowledge_agent=self.knowledge_agent,
            preferred_provider=preferred_provider
        )
        
        # 视觉评论Agent使用支持多模态的提供商
        self.visual_critic_agent = VisualCriticAgent(
            config=visual_config
        )
```

## 配置选项

### LLM管理器配置

```python
from llm_providers import LLMManagerConfig, FallbackStrategy, LoadBalancingStrategy

config = LLMManagerConfig(
    # 回退策略
    fallback_strategy=FallbackStrategy.CHEAPEST_FIRST,
    
    # 负载均衡策略
    load_balancing_strategy=LoadBalancingStrategy.LEAST_COST,
    
    # 健康检查
    enable_health_checks=True,
    health_check_interval=300,  # 5分钟
    
    # 阈值设置
    cost_threshold=0.10,        # 最大成本 $0.10/请求
    latency_threshold=30.0,     # 最大延迟 30秒
    
    # 提供商权重
    provider_weights={
        'openai': 1.0,
        'gemini': 2.0,    # 更高权重
        'deepseek': 1.5,
        'kimi': 1.0
    }
)
```

### 成本优化配置

```python
# 按成本排序的提供商配置
cost_optimized_config = {
    'budget': {
        'provider': 'deepseek',
        'model': 'deepseek-chat',
        'use_case': ['simple_tasks', 'code_generation']
    },
    'balanced': {
        'provider': 'openai',
        'model': 'gpt-4o-mini',
        'use_case': ['general_tasks', 'spec_generation']
    },
    'premium': {
        'provider': 'openai',
        'model': 'gpt-4o',
        'use_case': ['complex_analysis', 'visual_critique']
    }
}
```

## 测试和验证

### 1. 运行多LLM演示

```bash
python examples/multi_llm_demo.py
```

### 2. 测试现有功能

```bash
# 测试规格生成
python -c "
from agents.spec_generation_agent import SpecGenerationAgent
agent = SpecGenerationAgent()
print('Spec generation agent initialized successfully')
"

# 测试端到端流程
python demo_orchestrator_end_to_end.py
```

### 3. 性能监控

```python
# 获取提供商性能指标
from llm_providers import LLMManager

manager = LLMManager()
metrics = manager.get_metrics_summary()
for provider, stats in metrics.items():
    print(f"{provider}: {stats['success_rate']:.1%} success, ${stats['average_cost']:.4f} avg cost")
```

## 最佳实践

### 1. 提供商选择策略

- **代码生成**: DeepSeek (专门优化)
- **多模态任务**: OpenAI GPT-4o 或 Gemini
- **长文本处理**: Kimi (支持长上下文)
- **成本敏感任务**: Gemini Flash 或 DeepSeek

### 2. 错误处理

```python
try:
    response = agent.call_llm(messages)
except LLMError as e:
    if e.provider == LLMProviderType.OPENAI:
        # OpenAI特定错误处理
        pass
    else:
        # 通用错误处理
        pass
```

### 3. 监控和告警

```python
# 设置成本告警
if daily_cost > budget_threshold:
    switch_to_cheaper_provider()

# 设置性能告警
if average_latency > latency_threshold:
    switch_to_faster_provider()
```

## 故障排除

### 常见问题

1. **API密钥错误**: 检查 `.env` 文件中的API密钥配置
2. **模型不存在**: 验证模型名称是否正确
3. **网络连接问题**: 检查防火墙和代理设置
4. **配额限制**: 监控API使用量和配额

### 调试命令

```bash
# 检查提供商健康状态
python -c "
from llm_providers import create_llm_provider
provider = create_llm_provider('openai')
print(f'Health check: {provider.health_check()}')
"

# 测试所有提供商
python examples/multi_llm_demo.py
```

## 总结

通过这次升级，系统现在支持：

- ✅ 4种主流LLM提供商
- ✅ 自动回退和负载均衡
- ✅ 成本优化和性能监控
- ✅ 统一的API接口
- ✅ 向后兼容性

这为系统提供了更好的可靠性、成本效益和性能表现。
