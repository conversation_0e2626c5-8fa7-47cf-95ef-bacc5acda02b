# Blender 3D模型生成AI Agent系统 - 部署指南

## 目录

1. [部署概述](#部署概述)
2. [环境准备](#环境准备)
3. [本地部署](#本地部署)
4. [Docker部署](#docker部署)
5. [Kubernet<PERSON>部署](#kubernetes部署)
6. [配置管理](#配置管理)
7. [安全配置](#安全配置)
8. [性能优化](#性能优化)

## 部署概述

### 系统架构

本系统采用微服务架构，主要组件包括：

- **编排服务**: 主要的工作流协调器
- **图像处理服务**: 图像分析和预处理
- **知识服务**: 向量数据库和知识检索
- **代码生成服务**: LLM驱动的代码生成
- **Blender执行服务**: 3D模型生成和渲染
- **存储服务**: 文件和数据存储
- **监控服务**: 系统监控和日志

### 部署模式

- **开发模式**: 单机部署，适用于开发和测试
- **生产模式**: 分布式部署，支持高可用和负载均衡
- **云原生模式**: Kubernetes集群部署，支持自动扩缩容

## 环境准备

### 硬件要求

#### 最小配置
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 50GB可用空间
- GPU: 可选，用于加速图像分析

#### 推荐配置
- CPU: 8核心以上
- 内存: 16GB+ RAM
- 存储: 100GB+ SSD
- GPU: NVIDIA RTX 3060或更高

#### 生产环境
- CPU: 16核心以上
- 内存: 32GB+ RAM
- 存储: 500GB+ NVMe SSD
- GPU: 多卡配置，支持并行处理

### 软件依赖

#### 基础环境
- Ubuntu 20.04+ / CentOS 8+ / macOS 12+
- Python 3.11+
- Blender 4.0+
- Git
- curl/wget

#### 容器环境
- Docker 20.10+
- Docker Compose 2.0+
- Kubernetes 1.24+ (生产环境)

#### 外部服务
- OpenAI API (GPT-4, DALL-E)
- ChromaDB (可选，用于向量存储)
- PostgreSQL/MongoDB (可选，用于数据持久化)

## 本地部署

### 1. 环境配置

```bash
# 克隆项目
git clone <repository-url>
cd models

# 创建Conda环境
conda env create -f environment.yml
conda activate bl4.4env

# 安装Python依赖
pip install -r requirements.txt
```

### 2. 配置文件设置

```bash
# 复制环境变量模板
cp env_example .env

# 编辑配置文件
nano .env
```

关键配置项：
```bash
# Blender配置
BLENDER_PATH=/usr/bin/blender

# OpenAI API配置
OPENAI_API_KEY=your_api_key_here
OPENAI_MODEL=gpt-4

# 存储配置
OUTPUT_DIR=./output
LOGS_DIR=./logs

# 数据库配置（可选）
CHROMA_DB_PATH=./chroma_db
POSTGRES_URL=postgresql://user:pass@localhost:5432/blender_ai

# 监控配置
ENABLE_MONITORING=true
METRICS_PORT=8080
```

### 3. 初始化系统

```bash
# 创建必要目录
mkdir -p output/{models,renders,specs,code}
mkdir -p logs
mkdir -p chroma_db

# 初始化知识库
python -c "
from agents.knowledge_agent import KnowledgeAgent
agent = KnowledgeAgent()
agent.initialize_knowledge_base()
print('Knowledge base initialized')
"

# 验证Blender集成
python blender_interface/test_blender_env.py
```

### 4. 启动服务

```bash
# 启动主服务
python main_orchestrator.py --mode=server --port=8000

# 或者运行演示
python demo_orchestrator_end_to_end.py
```

## Docker部署

### 1. 构建镜像

创建`Dockerfile`:
```dockerfile
FROM ubuntu:22.04

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    python3.11 \
    python3-pip \
    blender \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY . .

# 安装Python依赖
RUN pip3 install -r requirements.txt

# 创建必要目录
RUN mkdir -p output logs chroma_db

# 设置环境变量
ENV BLENDER_PATH=/usr/bin/blender
ENV PYTHONPATH=/app

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python3", "main_orchestrator.py", "--mode=server", "--port=8000"]
```

构建和运行：
```bash
# 构建镜像
docker build -t blender-ai-agent .

# 运行容器
docker run -d \
  --name blender-ai \
  -p 8000:8000 \
  -v $(pwd)/output:/app/output \
  -v $(pwd)/logs:/app/logs \
  -e OPENAI_API_KEY=your_key \
  blender-ai-agent
```

### 2. Docker Compose部署

创建`docker-compose.yml`:
```yaml
version: '3.8'

services:
  blender-ai:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - BLENDER_PATH=/usr/bin/blender
      - POSTGRES_URL=********************************************/blender_ai
      - CHROMA_DB_PATH=/app/chroma_db
    volumes:
      - ./output:/app/output
      - ./logs:/app/logs
      - ./chroma_db:/app/chroma_db
    depends_on:
      - postgres
      - chroma

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=blender_ai
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  chroma:
    image: chromadb/chroma:latest
    ports:
      - "8001:8000"
    volumes:
      - chroma_data:/chroma/chroma

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - blender-ai

volumes:
  postgres_data:
  chroma_data:
```

启动服务：
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f blender-ai
```

## Kubernetes部署

### 1. 命名空间和配置

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: blender-ai

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: blender-ai-config
  namespace: blender-ai
data:
  BLENDER_PATH: "/usr/bin/blender"
  POSTGRES_URL: "********************************************/blender_ai"
  CHROMA_DB_PATH: "/app/chroma_db"
  ENABLE_MONITORING: "true"

---
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: blender-ai-secrets
  namespace: blender-ai
type: Opaque
data:
  OPENAI_API_KEY: <base64-encoded-key>
```

### 2. 存储配置

```yaml
# storage.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: blender-ai-output
  namespace: blender-ai
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: chroma-data
  namespace: blender-ai
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
```

### 3. 应用部署

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blender-ai
  namespace: blender-ai
spec:
  replicas: 3
  selector:
    matchLabels:
      app: blender-ai
  template:
    metadata:
      labels:
        app: blender-ai
    spec:
      containers:
      - name: blender-ai
        image: blender-ai-agent:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: blender-ai-config
        - secretRef:
            name: blender-ai-secrets
        volumeMounts:
        - name: output-storage
          mountPath: /app/output
        - name: chroma-storage
          mountPath: /app/chroma_db
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
          limits:
            memory: "8Gi"
            cpu: "4"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: output-storage
        persistentVolumeClaim:
          claimName: blender-ai-output
      - name: chroma-storage
        persistentVolumeClaim:
          claimName: chroma-data

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: blender-ai-service
  namespace: blender-ai
spec:
  selector:
    app: blender-ai
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP

---
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: blender-ai-ingress
  namespace: blender-ai
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - blender-ai.yourdomain.com
    secretName: blender-ai-tls
  rules:
  - host: blender-ai.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: blender-ai-service
            port:
              number: 80
```

### 4. 部署命令

```bash
# 应用配置
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f secret.yaml
kubectl apply -f storage.yaml
kubectl apply -f deployment.yaml

# 检查部署状态
kubectl get pods -n blender-ai
kubectl get services -n blender-ai
kubectl get ingress -n blender-ai

# 查看日志
kubectl logs -f deployment/blender-ai -n blender-ai
```

## 配置管理

### 环境变量配置

| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| BLENDER_PATH | Blender可执行文件路径 | /usr/bin/blender | 是 |
| OPENAI_API_KEY | OpenAI API密钥 | - | 是 |
| OPENAI_MODEL | 使用的GPT模型 | gpt-4 | 否 |
| OUTPUT_DIR | 输出文件目录 | ./output | 否 |
| LOGS_DIR | 日志文件目录 | ./logs | 否 |
| CHROMA_DB_PATH | ChromaDB数据路径 | ./chroma_db | 否 |
| ENABLE_MONITORING | 启用监控 | true | 否 |
| METRICS_PORT | 监控端口 | 8080 | 否 |

### 性能调优配置

```yaml
# performance.yaml
orchestrator:
  max_concurrent_tasks: 5
  task_timeout_minutes: 30
  enable_inner_loop: true
  enable_outer_loop: true
  max_iterations: 3

image_analysis:
  batch_size: 4
  gpu_memory_fraction: 0.8
  model_cache_size: 2

code_generation:
  max_tokens: 4096
  temperature: 0.1
  timeout_seconds: 120

blender_execution:
  headless_mode: true
  render_engine: "CYCLES"
  samples: 128
  timeout_minutes: 10
```

## 安全配置

### 1. API安全

```bash
# 生成API密钥
openssl rand -hex 32

# 配置JWT密钥
export JWT_SECRET_KEY=your_jwt_secret_here
export JWT_ALGORITHM=HS256
export JWT_EXPIRATION_HOURS=24
```

### 2. 网络安全

```yaml
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: blender-ai-network-policy
  namespace: blender-ai
spec:
  podSelector:
    matchLabels:
      app: blender-ai
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8000
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS
    - protocol: TCP
      port: 53   # DNS
    - protocol: UDP
      port: 53   # DNS
```

### 3. 文件权限

```bash
# 设置适当的文件权限
chmod 755 /app
chmod 644 /app/*.py
chmod 600 /app/.env
chmod 755 /app/output
chmod 755 /app/logs

# 创建专用用户
useradd -r -s /bin/false blender-ai
chown -R blender-ai:blender-ai /app
```

## 性能优化

### 1. 资源配置

```yaml
# 容器资源限制
resources:
  requests:
    memory: "4Gi"
    cpu: "2"
    nvidia.com/gpu: 1
  limits:
    memory: "8Gi"
    cpu: "4"
    nvidia.com/gpu: 1
```

### 2. 缓存配置

```python
# 配置Redis缓存
REDIS_URL = "redis://redis:6379/0"
CACHE_TTL = 3600  # 1小时

# 配置模型缓存
MODEL_CACHE_SIZE = 1024 * 1024 * 1024  # 1GB
KNOWLEDGE_CACHE_SIZE = 512 * 1024 * 1024  # 512MB
```

### 3. 数据库优化

```sql
-- PostgreSQL优化
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
```

### 4. 监控配置

```yaml
# prometheus.yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'blender-ai'
    static_configs:
      - targets: ['blender-ai-service:8080']
    metrics_path: /metrics
    scrape_interval: 30s
```

---

*部署完成后，请参考监控指南进行系统监控配置。*
