# Blender 3D模型生成AI Agent系统 - 监控指南

## 目录

1. [监控概述](#监控概述)
2. [关键指标](#关键指标)
3. [日志管理](#日志管理)
4. [性能监控](#性能监控)
5. [告警配置](#告警配置)
6. [故障诊断](#故障诊断)
7. [监控工具集成](#监控工具集成)

## 监控概述

### 监控架构

本系统采用多层监控架构：

- **应用层监控**: 业务指标、API性能、任务成功率
- **服务层监控**: 各Agent性能、资源使用、错误率
- **基础设施监控**: CPU、内存、磁盘、网络
- **外部依赖监控**: OpenAI API、数据库、存储服务

### 监控目标

- **可用性**: 系统正常运行时间 >99.5%
- **性能**: 端到端任务完成时间 <10分钟
- **质量**: 模型生成成功率 >85%
- **资源**: CPU使用率 <80%, 内存使用率 <85%

## 关键指标

### 1. 业务指标

#### 任务执行指标
```python
# 任务成功率
task_success_rate = successful_tasks / total_tasks * 100

# 平均任务执行时间
avg_task_duration = sum(task_durations) / len(task_durations)

# 各阶段耗时分布
stage_durations = {
    'image_analysis': [],
    'spec_generation': [],
    'code_generation': [],
    'blender_execution': [],
    'validation': []
}
```

#### 质量指标
```python
# 模型生成质量评分
model_quality_score = visual_critic_scores.mean()

# 代码生成成功率
code_generation_success_rate = valid_codes / total_codes * 100

# 错误修复成功率
error_fix_success_rate = fixed_errors / total_errors * 100
```

### 2. 系统性能指标

#### API性能
```python
# API响应时间
api_response_times = {
    'upload_image': [],
    'start_modeling': [],
    'task_status': [],
    'download_model': []
}

# API错误率
api_error_rates = {
    '4xx_errors': client_errors / total_requests,
    '5xx_errors': server_errors / total_requests
}

# 并发处理能力
concurrent_tasks = active_tasks_count
max_concurrent_capacity = 10
```

#### 资源使用
```python
# CPU使用率
cpu_usage_percent = psutil.cpu_percent(interval=1)

# 内存使用率
memory_info = psutil.virtual_memory()
memory_usage_percent = memory_info.percent

# GPU使用率（如果有）
gpu_usage = nvidia_ml_py.nvmlDeviceGetUtilizationRates(handle)

# 磁盘使用率
disk_usage = psutil.disk_usage('/').percent
```

### 3. Agent性能指标

#### 图像分析Agent
```python
metrics = {
    'analysis_accuracy': 0.92,
    'processing_time_seconds': 2.5,
    'confidence_scores': [0.95, 0.88, 0.91],
    'error_rate': 0.03
}
```

#### 代码生成Agent
```python
metrics = {
    'code_syntax_validity': 0.98,
    'execution_success_rate': 0.89,
    'average_code_length': 150,
    'llm_token_usage': 2048
}
```

#### Blender执行
```python
metrics = {
    'execution_success_rate': 0.91,
    'average_render_time': 45.2,
    'memory_peak_usage_mb': 2048,
    'crash_rate': 0.02
}
```

## 日志管理

### 1. 日志结构

#### 标准日志格式
```json
{
    "timestamp": "2025-07-19T14:30:00.123Z",
    "level": "INFO",
    "component": "ImageAnalysisAgent",
    "task_id": "uuid-1234",
    "message": "Image analysis completed successfully",
    "metadata": {
        "image_path": "/path/to/image.png",
        "processing_time": 2.5,
        "confidence_score": 0.92
    },
    "trace_id": "trace-5678"
}
```

#### 错误日志格式
```json
{
    "timestamp": "2025-07-19T14:35:00.456Z",
    "level": "ERROR",
    "component": "BlenderExecutor",
    "task_id": "uuid-1234",
    "message": "Blender execution failed",
    "error": {
        "type": "BlenderExecutionError",
        "message": "bpy.ops.mesh.primitive_cube_add() failed",
        "stack_trace": "...",
        "error_code": "BLENDER_API_ERROR"
    },
    "metadata": {
        "script_path": "/path/to/script.py",
        "blender_version": "4.0.0"
    },
    "trace_id": "trace-5678"
}
```

### 2. 日志配置

#### Python日志配置
```python
import logging
import json
from datetime import datetime

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'component': record.name,
            'message': record.getMessage(),
            'trace_id': getattr(record, 'trace_id', None)
        }
        
        if hasattr(record, 'task_id'):
            log_entry['task_id'] = record.task_id
            
        if hasattr(record, 'metadata'):
            log_entry['metadata'] = record.metadata
            
        if record.exc_info:
            log_entry['error'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'stack_trace': self.formatException(record.exc_info)
            }
            
        return json.dumps(log_entry)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    handlers=[
        logging.FileHandler('logs/application.log'),
        logging.StreamHandler()
    ]
)

# 为每个组件设置专用logger
for component in ['ImageAnalysisAgent', 'CodeGenerationAgent', 'BlenderExecutor']:
    logger = logging.getLogger(component)
    handler = logging.FileHandler(f'logs/{component.lower()}.log')
    handler.setFormatter(JSONFormatter())
    logger.addHandler(handler)
```

### 3. 日志轮转和清理

```bash
# logrotate配置
cat > /etc/logrotate.d/blender-ai << EOF
/app/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 blender-ai blender-ai
    postrotate
        systemctl reload blender-ai
    endscript
}
EOF
```

## 性能监控

### 1. 内置性能监控

```python
# tests/test_performance_monitoring.py 的使用示例
from tests.test_performance_monitoring import PerformanceMonitor

class SystemMonitor:
    def __init__(self):
        self.monitor = PerformanceMonitor()
        self.metrics_history = []
    
    def monitor_task_execution(self, task_func, *args, **kwargs):
        """监控任务执行性能"""
        with self.monitor.start_monitoring("TaskExecution", task_func.__name__):
            result = task_func(*args, **kwargs)
        
        # 收集性能指标
        summary = self.monitor.get_metrics_summary()
        self.metrics_history.append({
            'timestamp': datetime.now(),
            'function': task_func.__name__,
            'duration': summary.get('average_time', 0),
            'memory_usage': summary.get('average_memory_usage_mb', 0),
            'success': result is not None
        })
        
        return result
    
    def get_performance_report(self):
        """生成性能报告"""
        if not self.metrics_history:
            return {}
        
        return {
            'total_tasks': len(self.metrics_history),
            'success_rate': sum(1 for m in self.metrics_history if m['success']) / len(self.metrics_history),
            'average_duration': sum(m['duration'] for m in self.metrics_history) / len(self.metrics_history),
            'average_memory': sum(m['memory_usage'] for m in self.metrics_history) / len(self.metrics_history),
            'peak_memory': max(m['memory_usage'] for m in self.metrics_history)
        }
```

### 2. 实时监控端点

```python
from flask import Flask, jsonify
import psutil
import time

app = Flask(__name__)

@app.route('/metrics')
def metrics():
    """Prometheus格式的指标端点"""
    metrics_data = []
    
    # 系统指标
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    metrics_data.extend([
        f'system_cpu_usage_percent {cpu_percent}',
        f'system_memory_usage_percent {memory.percent}',
        f'system_memory_available_bytes {memory.available}',
        f'system_disk_usage_percent {disk.percent}',
        f'system_disk_free_bytes {disk.free}'
    ])
    
    # 应用指标
    from main_orchestrator import OrchestratorAgent
    if hasattr(OrchestratorAgent, '_instance'):
        orchestrator = OrchestratorAgent._instance
        metrics_data.extend([
            f'active_tasks_count {len(orchestrator.active_tasks)}',
            f'completed_tasks_total {orchestrator.completed_tasks_count}',
            f'failed_tasks_total {orchestrator.failed_tasks_count}'
        ])
    
    return '\n'.join(metrics_data), 200, {'Content-Type': 'text/plain'}

@app.route('/health')
def health():
    """健康检查端点"""
    health_status = {
        'status': 'healthy',
        'timestamp': time.time(),
        'checks': {}
    }
    
    # 检查Blender可用性
    try:
        from blender_interface.blender_executor import BlenderExecutor
        executor = BlenderExecutor()
        health_status['checks']['blender'] = 'ok'
    except Exception as e:
        health_status['checks']['blender'] = f'error: {str(e)}'
        health_status['status'] = 'unhealthy'
    
    # 检查OpenAI API
    try:
        import openai
        openai.models.list()
        health_status['checks']['openai_api'] = 'ok'
    except Exception as e:
        health_status['checks']['openai_api'] = f'error: {str(e)}'
        health_status['status'] = 'degraded'
    
    # 检查存储空间
    disk_usage = psutil.disk_usage('/').percent
    if disk_usage > 90:
        health_status['checks']['disk_space'] = f'warning: {disk_usage}% used'
        health_status['status'] = 'degraded'
    else:
        health_status['checks']['disk_space'] = 'ok'
    
    status_code = 200 if health_status['status'] == 'healthy' else 503
    return jsonify(health_status), status_code

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080)
```

## 告警配置

### 1. 告警规则

#### Prometheus告警规则
```yaml
# alerts.yml
groups:
  - name: blender-ai-alerts
    rules:
      # 系统资源告警
      - alert: HighCPUUsage
        expr: system_cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% for more than 5 minutes"
      
      - alert: HighMemoryUsage
        expr: system_memory_usage_percent > 85
        for: 3m
        labels:
          severity: critical
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% for more than 3 minutes"
      
      - alert: DiskSpaceLow
        expr: system_disk_usage_percent > 90
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Disk space running low"
          description: "Disk usage is {{ $value }}%"
      
      # 应用性能告警
      - alert: HighTaskFailureRate
        expr: rate(failed_tasks_total[5m]) / rate(completed_tasks_total[5m]) > 0.15
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High task failure rate"
          description: "Task failure rate is {{ $value | humanizePercentage }}"
      
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ $labels.instance }} has been down for more than 1 minute"
```

### 2. 告警通知

#### Slack通知配置
```python
import requests
import json

class AlertManager:
    def __init__(self, slack_webhook_url):
        self.slack_webhook_url = slack_webhook_url
    
    def send_alert(self, alert_type, message, severity='info'):
        """发送告警通知"""
        color_map = {
            'info': '#36a64f',
            'warning': '#ff9900',
            'critical': '#ff0000'
        }
        
        payload = {
            'attachments': [{
                'color': color_map.get(severity, '#36a64f'),
                'title': f'Blender AI Alert - {alert_type}',
                'text': message,
                'timestamp': int(time.time()),
                'fields': [
                    {
                        'title': 'Severity',
                        'value': severity.upper(),
                        'short': True
                    },
                    {
                        'title': 'Environment',
                        'value': os.getenv('ENVIRONMENT', 'production'),
                        'short': True
                    }
                ]
            }]
        }
        
        try:
            response = requests.post(
                self.slack_webhook_url,
                data=json.dumps(payload),
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            response.raise_for_status()
        except Exception as e:
            print(f"Failed to send alert: {e}")
    
    def check_and_alert(self):
        """检查系统状态并发送告警"""
        # 检查CPU使用率
        cpu_usage = psutil.cpu_percent(interval=1)
        if cpu_usage > 80:
            self.send_alert(
                'High CPU Usage',
                f'CPU usage is {cpu_usage:.1f}%',
                'warning' if cpu_usage < 90 else 'critical'
            )
        
        # 检查内存使用率
        memory = psutil.virtual_memory()
        if memory.percent > 85:
            self.send_alert(
                'High Memory Usage',
                f'Memory usage is {memory.percent:.1f}%',
                'critical'
            )
        
        # 检查磁盘空间
        disk = psutil.disk_usage('/')
        if disk.percent > 90:
            self.send_alert(
                'Low Disk Space',
                f'Disk usage is {disk.percent:.1f}%',
                'critical'
            )
```

## 故障诊断

### 1. 常见问题诊断

#### 任务执行失败
```python
def diagnose_task_failure(task_id):
    """诊断任务失败原因"""
    diagnosis = {
        'task_id': task_id,
        'failure_stage': None,
        'error_type': None,
        'recommendations': []
    }
    
    # 分析日志
    log_entries = get_task_logs(task_id)
    error_logs = [log for log in log_entries if log['level'] == 'ERROR']
    
    if error_logs:
        latest_error = error_logs[-1]
        diagnosis['failure_stage'] = latest_error.get('component')
        diagnosis['error_type'] = latest_error.get('error', {}).get('type')
        
        # 根据错误类型提供建议
        if 'BlenderExecutionError' in diagnosis['error_type']:
            diagnosis['recommendations'].extend([
                'Check Blender installation and version compatibility',
                'Verify generated Python script syntax',
                'Check available system memory'
            ])
        elif 'OpenAIAPIError' in diagnosis['error_type']:
            diagnosis['recommendations'].extend([
                'Verify OpenAI API key and quota',
                'Check network connectivity',
                'Reduce request frequency'
            ])
    
    return diagnosis
```

### 2. 性能问题诊断

```python
def diagnose_performance_issues():
    """诊断性能问题"""
    issues = []
    
    # 检查系统资源
    cpu_usage = psutil.cpu_percent(interval=5)
    memory = psutil.virtual_memory()
    
    if cpu_usage > 80:
        issues.append({
            'type': 'high_cpu_usage',
            'severity': 'warning',
            'value': cpu_usage,
            'recommendations': [
                'Scale up CPU resources',
                'Optimize agent algorithms',
                'Reduce concurrent task limit'
            ]
        })
    
    if memory.percent > 85:
        issues.append({
            'type': 'high_memory_usage',
            'severity': 'critical',
            'value': memory.percent,
            'recommendations': [
                'Increase available memory',
                'Implement memory caching strategies',
                'Optimize model loading'
            ]
        })
    
    # 检查任务队列
    active_tasks = get_active_tasks_count()
    if active_tasks > 10:
        issues.append({
            'type': 'task_queue_backlog',
            'severity': 'warning',
            'value': active_tasks,
            'recommendations': [
                'Scale out worker instances',
                'Optimize task processing time',
                'Implement task prioritization'
            ]
        })
    
    return issues
```

## 监控工具集成

### 1. Grafana仪表板

```json
{
  "dashboard": {
    "title": "Blender AI Agent System",
    "panels": [
      {
        "title": "Task Success Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(completed_tasks_total[5m]) / (rate(completed_tasks_total[5m]) + rate(failed_tasks_total[5m])) * 100"
          }
        ]
      },
      {
        "title": "System Resources",
        "type": "graph",
        "targets": [
          {"expr": "system_cpu_usage_percent", "legendFormat": "CPU %"},
          {"expr": "system_memory_usage_percent", "legendFormat": "Memory %"},
          {"expr": "system_disk_usage_percent", "legendFormat": "Disk %"}
        ]
      },
      {
        "title": "Task Duration Distribution",
        "type": "heatmap",
        "targets": [
          {"expr": "histogram_quantile(0.95, task_duration_seconds_bucket)"}
        ]
      }
    ]
  }
}
```

### 2. ELK Stack集成

```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /app/logs/*.log
  json.keys_under_root: true
  json.add_error_key: true

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "blender-ai-logs-%{+yyyy.MM.dd}"

setup.template.name: "blender-ai"
setup.template.pattern: "blender-ai-*"
```

```json
// Kibana索引模板
{
  "index_patterns": ["blender-ai-*"],
  "mappings": {
    "properties": {
      "timestamp": {"type": "date"},
      "level": {"type": "keyword"},
      "component": {"type": "keyword"},
      "task_id": {"type": "keyword"},
      "message": {"type": "text"},
      "trace_id": {"type": "keyword"},
      "metadata": {"type": "object"}
    }
  }
}
```

---

*监控系统配置完成后，建议定期检查告警规则的有效性，并根据实际运行情况调整阈值。*
