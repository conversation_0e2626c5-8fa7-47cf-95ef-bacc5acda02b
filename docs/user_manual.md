# Blender 3D模型生成AI Agent系统 - 用户手册

## 目录

1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [功能详解](#功能详解)
4. [API接口说明](#api接口说明)
5. [常见问题](#常见问题)
6. [故障排除](#故障排除)

## 系统概述

Blender 3D模型生成AI Agent系统是一个基于人工智能的端到端3D建模解决方案，能够从图像输入自动生成高质量的Blender 3D模型。

### 核心功能

- **图像分析**: 智能识别图像中的3D几何体、材质、颜色等特征
- **规格生成**: 自动生成符合标准的3D模型规格文件
- **代码生成**: 将规格转换为可执行的Blender Python脚本
- **模型生成**: 在Blender中执行脚本，生成最终的3D模型
- **质量控制**: 自动验证和调试，确保模型质量
- **视觉反馈**: 智能比较生成结果与原始图像，提供改进建议

### 支持的输入格式

- **图像格式**: PNG, JPG, BMP
- **图像来源**: 本地文件、URL链接、AI生成图像（DALL-E集成）
- **分辨率**: 建议256x256像素以上

### 输出格式

- **3D模型**: .blend文件（Blender原生格式）
- **渲染图**: PNG格式的预览图
- **规格文件**: JSON格式的模型规格
- **代码文件**: Python脚本文件

## 快速开始

### 环境要求

- Python 3.11+
- Blender 4.0+
- 8GB+ RAM
- GPU推荐（用于图像分析和渲染）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd models
```

2. **创建环境**
```bash
conda env create -f environment.yml
conda activate bl4.4env
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp env_example .env
# 编辑.env文件，设置BLENDER_PATH等变量
```

5. **验证安装**
```bash
python -m pytest tests/test_blender_executor.py -v
```

### 第一个模型

1. **准备图像**
   - 将图像文件放在`demo_images/`目录下
   - 确保图像清晰，包含明确的几何形状

2. **运行端到端演示**
```bash
python demo_orchestrator_end_to_end.py
```

3. **查看结果**
   - 生成的模型: `output/models/`
   - 渲染图: `output/renders/`
   - 日志文件: `logs/`

## 功能详解

### 图像分析

系统支持多种分析粒度：

- **BASIC**: 基础形状识别（立方体、球体、圆柱体等）
- **DETAILED**: 详细特征分析（材质、纹理、相对位置）
- **COMPREHENSIVE**: 全面场景理解（深度、遮挡、复杂结构）

```python
from agents.image_analysis_agent import ImageAnalysisAgent, AnalysisGranularity

agent = ImageAnalysisAgent()
result = agent.analyze_image(
    image_path="path/to/image.png",
    granularity=AnalysisGranularity.DETAILED
)
```

### 规格生成

支持两个版本的规格Schema：

- **v1.0.0**: 基础几何体和材质
- **v2.0.0**: 复杂材质、修改器、动画、MCP结构

```python
from agents.spec_generation_agent import SpecGenerationAgent, SpecGenerationConfig

config = SpecGenerationConfig(
    schema_version="v2.0.0",
    enable_complex_materials=True,
    enable_modifiers=True
)
agent = SpecGenerationAgent(config=config)
```

### 代码生成

支持多种Blender功能：

- **基础几何体**: 立方体、球体、圆柱体、平面、圆锥
- **复杂材质**: PBR、玻璃、发光、次表面散射等
- **修改器**: 阵列、镜像、实体化、倒角等
- **MCP结构**: 分子模型、蛋白质结构

### 质量控制

- **语法检查**: AST静态分析
- **执行验证**: Blender环境测试
- **错误诊断**: 智能错误分类和修复建议
- **性能优化**: 代码效率分析

## API接口说明

### 主要API端点

#### 1. 图像上传
```http
POST /upload_image
Content-Type: multipart/form-data

{
  "image": <file>,
  "metadata": {
    "name": "my_image",
    "description": "A red cube"
  }
}
```

#### 2. 开始建模任务
```http
POST /start_modeling
Content-Type: application/json

{
  "image_id": "uuid-1234",
  "preferences": {
    "units": "meters",
    "quality": "high",
    "style": "realistic"
  },
  "model_name": "MyModel",
  "description": "Generated from uploaded image"
}
```

#### 3. 查询任务状态
```http
GET /task_status/{task_id}

Response:
{
  "task_id": "uuid-5678",
  "status": "processing",
  "progress": 65,
  "current_stage": "code_generation",
  "estimated_completion": "2025-07-19T15:30:00Z"
}
```

#### 4. 下载模型
```http
GET /download_model/{model_id}

Response: Binary .blend file
```

### Python SDK

```python
from main_orchestrator import OrchestratorAgent, OrchestrationConfig

# 初始化编排器
config = OrchestrationConfig(
    enable_inner_loop=True,
    enable_outer_loop=True,
    max_iterations=3
)
orchestrator = OrchestratorAgent(config=config)

# 执行建模任务
result = orchestrator.orchestrate_task(
    image_path="path/to/image.png",
    user_preferences={"units": "meters"},
    model_name="MyModel",
    description="Test model"
)

print(f"Model generated: {result.model_path}")
print(f"Render saved: {result.render_path}")
```

## 常见问题

### Q: 支持哪些类型的图像？
A: 系统支持包含清晰几何形状的图像，如建筑物、产品设计、简单场景等。抽象艺术或复杂自然场景可能效果不佳。

### Q: 生成的模型质量如何？
A: 模型质量取决于输入图像的清晰度和复杂度。简单几何体通常能达到90%+的准确率，复杂场景可能需要人工调整。

### Q: 可以自定义材质吗？
A: 是的，系统支持多种材质类型，包括PBR、玻璃、金属等。可以通过用户偏好设置或直接修改生成的规格文件。

### Q: 支持动画吗？
A: v2.0.0版本支持基础关键帧动画。复杂动画建议在Blender中手动调整。

### Q: 如何提高生成速度？
A: 可以通过以下方式优化：
- 使用GPU加速
- 降低图像分析粒度
- 禁用不必要的功能（如外循环反馈）
- 使用更快的LLM模型

## 故障排除

### 常见错误

#### 1. Blender执行失败
**症状**: `BlenderExecutor`报错，无法生成模型
**解决方案**:
- 检查Blender路径配置
- 确认Blender版本兼容性（推荐4.0+）
- 查看详细错误日志

#### 2. 图像分析失败
**症状**: 无法识别图像中的对象
**解决方案**:
- 确保图像清晰度足够
- 尝试不同的分析粒度
- 检查图像格式是否支持

#### 3. 内存不足
**症状**: 系统运行缓慢或崩溃
**解决方案**:
- 增加系统内存
- 降低图像分辨率
- 关闭其他占用内存的程序

#### 4. API调用失败
**症状**: OpenAI API或其他外部服务调用失败
**解决方案**:
- 检查API密钥配置
- 确认网络连接
- 查看API使用限额

### 日志分析

系统日志位于`logs/`目录下：

- `orchestrator.log`: 主要工作流日志
- `agents/`: 各Agent的详细日志
- `blender_execution.log`: Blender执行日志
- `errors.log`: 错误和异常日志

### 性能监控

使用内置的性能监控工具：

```python
from tests.test_performance_monitoring import PerformanceMonitor

monitor = PerformanceMonitor()
with monitor.start_monitoring("ImageAnalysis", "analyze_complex_scene"):
    # 执行图像分析
    result = agent.analyze_image(image_path)

# 查看性能报告
summary = monitor.get_metrics_summary()
print(f"平均执行时间: {summary['average_time']:.2f}s")
print(f"内存使用: {summary['average_memory_usage_mb']:.1f}MB")
```

### 技术支持

如需技术支持，请提供以下信息：

1. 系统环境信息（Python版本、Blender版本、操作系统）
2. 完整的错误日志
3. 输入图像（如果可能）
4. 复现步骤

---

*本手册持续更新中，最新版本请参考项目文档。*
