# Environment variables for the Blender 3D Model Generation AI Agent project.
# These variables are typically loaded by a tool like `python-dotenv`.

# BLENDER_PATH: Specifies the absolute path to the Blender executable.
# Example: /usr/bin/blender or /Applications/Blender.app/Contents/MacOS/Blender
# Please update this path to match your Blender installation.
BLENDER_PATH=/path/to/your/blender/executable

# =============================================================================
# LLM Provider Configuration
# =============================================================================

# OpenAI Configuration (Primary)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini
OPENAI_BASE_URL=https://api.openai.com/v1

# Google Gemini Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-1.5-flash

# DeepSeek Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1

# Kimi (月之暗面) Configuration
KIMI_API_KEY=your_kimi_api_key_here
KIMI_MODEL=moonshot-v1-8k
KIMI_BASE_URL=https://api.moonshot.cn/v1

# =============================================================================
# LLM Manager Configuration
# =============================================================================

# Primary LLM provider (openai, gemini, deepseek, kimi)
PRIMARY_LLM_PROVIDER=openai

# Enable fallback to other providers if primary fails
ENABLE_LLM_FALLBACK=true

# Load balancing strategy (round_robin, random, least_cost, least_latency, weighted)
LLM_LOAD_BALANCING=least_cost

# Cost threshold per request (USD)
LLM_COST_THRESHOLD=0.10

# Latency threshold per request (seconds)
LLM_LATENCY_THRESHOLD=30.0

# Enable health checks for providers
ENABLE_LLM_HEALTH_CHECKS=true

# =============================================================================
# Embedding Configuration
# =============================================================================

# Embedding provider (openai is recommended as others may not support embeddings)
EMBEDDING_PROVIDER=openai
EMBEDDING_MODEL=text-embedding-3-small

# =============================================================================
# Application Configuration
# =============================================================================

# Output directories
OUTPUT_DIR=./output
LOGS_DIR=./logs
CHROMA_DB_PATH=./chroma_db

# Monitoring
ENABLE_MONITORING=true
METRICS_PORT=8080

# Debug mode
DEBUG_MODE=false
