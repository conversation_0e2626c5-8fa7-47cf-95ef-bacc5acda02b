#!/bin/bash

# Blender 3D模型生成AI Agent系统 - 自动化部署脚本
# 版本: 1.0.0
# 作者: Augment Agent
# 日期: 2025-07-19

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_MODE="${1:-local}"
ENVIRONMENT="${2:-development}"
BLENDER_VERSION="${BLENDER_VERSION:-4.0}"
PYTHON_VERSION="${PYTHON_VERSION:-3.11}"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 错误处理
error_exit() {
    log_error "$1"
    exit 1
}

# 显示帮助信息
show_help() {
    cat << EOF
Blender AI Agent 部署脚本

用法: $0 [部署模式] [环境]

部署模式:
  local       本地部署 (默认)
  docker      Docker容器部署
  k8s         Kubernetes集群部署

环境:
  development 开发环境 (默认)
  staging     测试环境
  production  生产环境

示例:
  $0 local development
  $0 docker staging
  $0 k8s production

环境变量:
  BLENDER_VERSION   Blender版本 (默认: 4.0)
  PYTHON_VERSION    Python版本 (默认: 3.11)
  OPENAI_API_KEY    OpenAI API密钥
  DEPLOYMENT_CONFIG 部署配置文件路径

EOF
}

# 检查系统要求
check_system_requirements() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    else
        error_exit "不支持的操作系统: $OSTYPE"
    fi
    
    # 检查必需的命令
    local required_commands=("git" "curl" "python3")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error_exit "缺少必需的命令: $cmd"
        fi
    done
    
    # 检查Python版本
    local python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$python_version >= 3.11" | bc -l) -eq 0 ]]; then
        error_exit "需要Python 3.11或更高版本，当前版本: $python_version"
    fi
    
    # 检查可用内存
    if [[ "$OS" == "linux" ]]; then
        local mem_gb=$(free -g | awk '/^Mem:/{print $2}')
        if [[ $mem_gb -lt 8 ]]; then
            log_warning "建议至少8GB内存，当前: ${mem_gb}GB"
        fi
    fi
    
    log_success "系统要求检查完成"
}

# 安装系统依赖
install_system_dependencies() {
    log_info "安装系统依赖..."
    
    case "$OS" in
        "linux")
            if command -v apt-get &> /dev/null; then
                sudo apt-get update
                sudo apt-get install -y \
                    python3-pip \
                    python3-venv \
                    git \
                    curl \
                    wget \
                    build-essential \
                    libgl1-mesa-glx \
                    libglib2.0-0 \
                    libsm6 \
                    libxext6 \
                    libxrender-dev \
                    libgomp1
            elif command -v yum &> /dev/null; then
                sudo yum update -y
                sudo yum install -y \
                    python3-pip \
                    git \
                    curl \
                    wget \
                    gcc \
                    gcc-c++ \
                    make
            fi
            ;;
        "macos")
            if command -v brew &> /dev/null; then
                brew update
                brew install python@3.11 git curl wget
            else
                log_warning "建议安装Homebrew以管理依赖"
            fi
            ;;
    esac
    
    log_success "系统依赖安装完成"
}

# 安装Blender
install_blender() {
    log_info "检查Blender安装..."
    
    if command -v blender &> /dev/null; then
        local blender_version=$(blender --version | head -n1 | grep -oE '[0-9]+\.[0-9]+')
        log_info "发现Blender版本: $blender_version"
        
        if [[ $(echo "$blender_version >= $BLENDER_VERSION" | bc -l) -eq 1 ]]; then
            log_success "Blender版本满足要求"
            return 0
        fi
    fi
    
    log_info "安装Blender $BLENDER_VERSION..."
    
    case "$OS" in
        "linux")
            local blender_url="https://download.blender.org/release/Blender${BLENDER_VERSION}/blender-${BLENDER_VERSION}.0-linux-x64.tar.xz"
            local blender_dir="/opt/blender"
            
            sudo mkdir -p "$blender_dir"
            cd /tmp
            wget -O blender.tar.xz "$blender_url"
            sudo tar -xf blender.tar.xz -C "$blender_dir" --strip-components=1
            sudo ln -sf "$blender_dir/blender" /usr/local/bin/blender
            ;;
        "macos")
            if command -v brew &> /dev/null; then
                brew install --cask blender
            else
                error_exit "请手动安装Blender或安装Homebrew"
            fi
            ;;
    esac
    
    log_success "Blender安装完成"
}

# 设置Python环境
setup_python_environment() {
    log_info "设置Python环境..."
    
    cd "$PROJECT_ROOT"
    
    # 检查conda
    if command -v conda &> /dev/null; then
        log_info "使用Conda创建环境..."
        conda env create -f environment.yml --force
        log_info "激活Conda环境: conda activate bl4.4env"
    else
        log_info "使用venv创建虚拟环境..."
        python3 -m venv venv
        source venv/bin/activate
        pip install --upgrade pip
        pip install -r requirements.txt
    fi
    
    log_success "Python环境设置完成"
}

# 配置环境变量
setup_environment_config() {
    log_info "配置环境变量..."
    
    cd "$PROJECT_ROOT"
    
    # 复制环境变量模板
    if [[ ! -f .env ]]; then
        cp env_example .env
        log_info "已创建.env文件，请编辑配置"
    fi
    
    # 设置Blender路径
    local blender_path=$(which blender)
    if [[ -n "$blender_path" ]]; then
        sed -i.bak "s|BLENDER_PATH=.*|BLENDER_PATH=$blender_path|" .env
        log_info "已设置Blender路径: $blender_path"
    fi
    
    # 检查OpenAI API密钥
    if [[ -z "${OPENAI_API_KEY:-}" ]]; then
        log_warning "请设置OPENAI_API_KEY环境变量"
        echo "export OPENAI_API_KEY=your_api_key_here" >> .env
    fi
    
    log_success "环境配置完成"
}

# 初始化项目
initialize_project() {
    log_info "初始化项目..."
    
    cd "$PROJECT_ROOT"
    
    # 创建必要目录
    mkdir -p output/{models,renders,specs,code}
    mkdir -p logs
    mkdir -p chroma_db
    mkdir -p temp
    
    # 设置权限
    chmod 755 output logs chroma_db temp
    chmod +x scripts/*.sh
    
    # 初始化知识库
    if command -v conda &> /dev/null; then
        conda run -n bl4.4env python -c "
from agents.knowledge_agent import KnowledgeAgent
try:
    agent = KnowledgeAgent()
    agent.initialize_knowledge_base()
    print('Knowledge base initialized successfully')
except Exception as e:
    print(f'Knowledge base initialization failed: {e}')
"
    else
        source venv/bin/activate
        python -c "
from agents.knowledge_agent import KnowledgeAgent
try:
    agent = KnowledgeAgent()
    agent.initialize_knowledge_base()
    print('Knowledge base initialized successfully')
except Exception as e:
    print(f'Knowledge base initialization failed: {e}')
"
    fi
    
    log_success "项目初始化完成"
}

# 运行测试
run_tests() {
    log_info "运行系统测试..."
    
    cd "$PROJECT_ROOT"
    
    # 基础功能测试
    if command -v conda &> /dev/null; then
        conda run -n bl4.4env python -m pytest tests/test_blender_executor.py -v --tb=short
    else
        source venv/bin/activate
        python -m pytest tests/test_blender_executor.py -v --tb=short
    fi
    
    log_success "测试完成"
}

# Docker部署
deploy_docker() {
    log_info "开始Docker部署..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        error_exit "Docker未安装，请先安装Docker"
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error_exit "Docker Compose未安装，请先安装Docker Compose"
    fi
    
    cd "$PROJECT_ROOT"
    
    # 构建镜像
    log_info "构建Docker镜像..."
    docker build -t blender-ai-agent:latest .
    
    # 启动服务
    log_info "启动Docker服务..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "Docker部署完成"
        log_info "服务访问地址: http://localhost:8000"
    else
        error_exit "Docker服务启动失败"
    fi
}

# Kubernetes部署
deploy_kubernetes() {
    log_info "开始Kubernetes部署..."
    
    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        error_exit "kubectl未安装，请先安装kubectl"
    fi
    
    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        error_exit "无法连接到Kubernetes集群"
    fi
    
    cd "$PROJECT_ROOT"
    
    # 应用Kubernetes配置
    log_info "应用Kubernetes配置..."
    
    # 创建命名空间
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Namespace
metadata:
  name: blender-ai
EOF
    
    # 创建ConfigMap
    kubectl create configmap blender-ai-config \
        --from-env-file=.env \
        --namespace=blender-ai \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # 部署应用
    if [[ -f k8s/deployment.yaml ]]; then
        kubectl apply -f k8s/ --namespace=blender-ai
    else
        log_warning "Kubernetes配置文件不存在，请手动创建"
    fi
    
    # 等待部署完成
    log_info "等待部署完成..."
    kubectl wait --for=condition=available --timeout=300s deployment/blender-ai --namespace=blender-ai
    
    log_success "Kubernetes部署完成"
}

# 本地部署
deploy_local() {
    log_info "开始本地部署..."
    
    check_system_requirements
    install_system_dependencies
    install_blender
    setup_python_environment
    setup_environment_config
    initialize_project
    run_tests
    
    log_success "本地部署完成"
    log_info "启动服务: python main_orchestrator.py --mode=server"
    log_info "运行演示: python demo_orchestrator_end_to_end.py"
}

# 部署后验证
post_deployment_verification() {
    log_info "执行部署后验证..."
    
    case "$DEPLOYMENT_MODE" in
        "local")
            # 验证本地服务
            if [[ -f "$PROJECT_ROOT/main_orchestrator.py" ]]; then
                log_success "主程序文件存在"
            else
                log_error "主程序文件缺失"
            fi
            ;;
        "docker")
            # 验证Docker服务
            if docker-compose ps | grep -q "Up"; then
                log_success "Docker服务运行正常"
            else
                log_error "Docker服务未正常运行"
            fi
            ;;
        "k8s")
            # 验证Kubernetes部署
            if kubectl get pods --namespace=blender-ai | grep -q "Running"; then
                log_success "Kubernetes Pod运行正常"
            else
                log_error "Kubernetes Pod未正常运行"
            fi
            ;;
    esac
    
    log_success "部署验证完成"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    rm -rf /tmp/blender.tar.xz
}

# 主函数
main() {
    # 检查参数
    if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
        show_help
        exit 0
    fi
    
    log_info "开始部署 Blender AI Agent 系统"
    log_info "部署模式: $DEPLOYMENT_MODE"
    log_info "环境: $ENVIRONMENT"
    
    # 设置错误处理
    trap cleanup EXIT
    
    # 根据部署模式执行相应操作
    case "$DEPLOYMENT_MODE" in
        "local")
            deploy_local
            ;;
        "docker")
            deploy_docker
            ;;
        "k8s"|"kubernetes")
            deploy_kubernetes
            ;;
        *)
            error_exit "不支持的部署模式: $DEPLOYMENT_MODE"
            ;;
    esac
    
    # 部署后验证
    post_deployment_verification
    
    log_success "部署完成！"
    log_info "请查看文档了解更多使用方法: docs/user_manual.md"
}

# 执行主函数
main "$@"
