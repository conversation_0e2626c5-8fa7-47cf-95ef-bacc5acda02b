#!/usr/bin/env python3
"""
Multi-LLM Support Verification Script

This script verifies that the multi-LLM provider architecture is correctly implemented
and can be imported without errors.

Usage:
    # In the correct conda environment
    conda activate bl4.4env
    python scripts/verify_multi_llm.py
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all multi-LLM components can be imported."""
    print("🔍 Testing Multi-LLM Architecture Imports...")
    
    try:
        # Test base classes
        from llm_providers.base import (
            LLMProvider, LLMConfig, LLMMessage, LLMResponse, 
            LLMProviderType, MessageRole, LLMError
        )
        print("   ✅ Base classes imported successfully")
        
        # Test factory
        from llm_providers.factory import LLMFactory, create_llm_provider
        print("   ✅ Factory classes imported successfully")
        
        # Test manager
        from llm_providers.manager import LLMManager, LLMManagerConfig
        print("   ✅ Manager classes imported successfully")
        
        # Test provider types
        supported_providers = [p.value for p in LLMProviderType]
        print(f"   📋 Supported provider types: {supported_providers}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import failed: {e}")
        return False

def test_provider_creation():
    """Test provider creation (without API calls)."""
    print("\n🏭 Testing Provider Creation...")
    
    try:
        from llm_providers.base import LLMConfig, LLMProviderType
        from llm_providers.factory import LLMFactory
        
        # Test configuration creation
        config = LLMConfig(
            provider=LLMProviderType.OPENAI,
            model="gpt-4o-mini",
            api_key="test_key",
            temperature=0.1
        )
        print("   ✅ LLM configuration created successfully")
        
        # Test factory registration
        supported = LLMFactory.get_supported_providers()
        print(f"   📋 Factory supports {len(supported)} providers")
        
        for provider_type in supported:
            default_model = LLMFactory.get_default_model(provider_type)
            print(f"   - {provider_type.value}: {default_model}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Provider creation test failed: {e}")
        return False

def test_manager_creation():
    """Test manager creation."""
    print("\n🎛️  Testing Manager Creation...")
    
    try:
        from llm_providers.manager import LLMManager, LLMManagerConfig
        from llm_providers.manager import FallbackStrategy, LoadBalancingStrategy
        
        # Test manager configuration
        config = LLMManagerConfig(
            fallback_strategy=FallbackStrategy.NEXT_AVAILABLE,
            load_balancing_strategy=LoadBalancingStrategy.ROUND_ROBIN,
            enable_health_checks=True
        )
        print("   ✅ Manager configuration created successfully")
        
        # Test manager creation
        manager = LLMManager(config)
        print("   ✅ LLM manager created successfully")
        
        # Test strategies
        strategies = [s.value for s in FallbackStrategy]
        print(f"   📋 Available fallback strategies: {strategies}")
        
        lb_strategies = [s.value for s in LoadBalancingStrategy]
        print(f"   📋 Available load balancing strategies: {lb_strategies}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Manager creation test failed: {e}")
        return False

def test_environment_config():
    """Test environment configuration."""
    print("\n🔧 Testing Environment Configuration...")
    
    # Check if .env file exists
    env_file = project_root / ".env"
    env_example = project_root / "env_exmple"
    
    if env_example.exists():
        print("   ✅ Environment template (env_exmple) exists")
        
        # Read and check for multi-LLM variables
        with open(env_example, 'r') as f:
            content = f.read()
            
        required_vars = [
            'PRIMARY_LLM_PROVIDER',
            'OPENAI_API_KEY',
            'GEMINI_API_KEY', 
            'DEEPSEEK_API_KEY',
            'KIMI_API_KEY',
            'ENABLE_LLM_FALLBACK'
        ]
        
        missing_vars = []
        for var in required_vars:
            if var not in content:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"   ⚠️  Missing environment variables: {missing_vars}")
        else:
            print("   ✅ All required environment variables defined")
            
    else:
        print("   ❌ Environment template file not found")
        return False
    
    return True

def test_documentation():
    """Test documentation files."""
    print("\n📚 Testing Documentation...")
    
    docs_to_check = [
        "docs/multi_llm_migration_guide.md",
        "docs/llm_support_analysis_report.md",
        "examples/multi_llm_demo.py"
    ]
    
    all_exist = True
    for doc_path in docs_to_check:
        full_path = project_root / doc_path
        if full_path.exists():
            print(f"   ✅ {doc_path} exists")
        else:
            print(f"   ❌ {doc_path} missing")
            all_exist = False
    
    return all_exist

def main():
    """Run all verification tests."""
    print("🚀 Multi-LLM Support Verification")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Provider Creation", test_provider_creation),
        ("Manager Creation", test_manager_creation),
        ("Environment Config", test_environment_config),
        ("Documentation", test_documentation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   💥 {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Verification Summary")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All tests passed! Multi-LLM support is ready.")
        print("\n💡 Next steps:")
        print("   1. Set up API keys in .env file")
        print("   2. Install optional dependencies: pip install google-generativeai")
        print("   3. Run the demo: python examples/multi_llm_demo.py")
        print("   4. Update existing agents to use new architecture")
    else:
        print(f"\n⚠️  {total-passed} tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
